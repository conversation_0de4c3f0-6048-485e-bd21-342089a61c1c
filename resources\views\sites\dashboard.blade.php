@extends('sites.content')
@section('contentsite')
@section('title', 'Dashboard Site')
<!-- mulai content disin -->
<div class="row bgwhite page-title-box shadow-kit mb-1 d-flex align-items-center">
    <div class="col" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
    </div>

    <div class="col">
        <form method="GET" action="{{ route('sites.dashboard') }}" class="p-2">
            <div class="row align-items-center">
                <div class="col-auto">
                    <div class="d-flex align-items-center gap-3">
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input custom-radio" name="group_by" id="day" value="day" {{ $groupBy === 'day' ? 'checked' : '' }}>
                            <label class="form-check-label" for="day">Day</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input custom-radio" name="group_by" id="week" value="week" {{ $groupBy === 'week' ? 'checked' : '' }}>
                            <label class="form-check-label" for="week">Week</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input type="radio" class="form-check-input custom-radio" name="group_by" id="month" value="month" {{ $groupBy === 'month' ? 'checked' : '' }}>
                            <label class="form-check-label" for="month">Month</label>
                        </div>
                    </div>
                </div>
                <div class="col-auto">
                    <input type="date" class="form-control" name="start_date" value="{{ $startDate }}">
                </div>
                <div class="col-auto">
                    <input type="date" class="form-control" name="end_date" value="{{ $endDate }}">
                </div>
            </div>
        </form>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row pt-1 pl-2">
    <div class="col p-0">
        <div class="shadow-kit bgchart">
            <h5 class="pl-4 pt-3 mt-0 text-uppercase">In & Out Stock Chart</h5>
            <div style="min-height: 400px;" class="chart-container">
                <canvas id="inOutStockChart"></canvas>
            </div>
        </div>
        @foreach($inventoryData as $data)
        @if(count($data['not_ready_parts']) > 0 || count($data['medium_parts']) > 0)
        <div class="pl-2 pr-4 shadow-kit bgwhite p-4 pb-0 mt-2">
            <h5 class="text-uppercase text-bold">Status Part di {{ $data['site_name'] }}</h5>
            <hr>
            @if(count($data['not_ready_parts']) > 0)
            <div class="table-responsive">
                <h6>Part Not Ready</h6>
                <table class="table table-bordered w-100" id="not-ready-table-{{ $loop->index }}">
                    <thead class="table-dark text-white">
                        <tr>
                            <th class="p-2">Nama Part</th>
                            <th class="p-2">Min</th>
                            <th class="p-2">Max</th>
                            <th class="p-2">Stock Tersisa</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($data['not_ready_parts'] as $part)
                        <tr class="{{ $part['status'] == 'danger' ? 'table-danger' : '' }}">
                            <td>{{ $part['part_name'] }}</td>
                            <td>{{ $part['min_stock'] }}</td>
                            <td>{{ $part['max_stock'] }}</td>
                            <td>{{ $part['stock_quantity'] }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                <div id="not-ready-pagination-{{ $loop->index }}" class="pagination-container mt-3">
                    <!-- Pagination will be rendered here by JavaScript -->
                </div>
            </div>
            @endif

            @if(count($data['medium_parts']) > 0)
            <div class="table-responsive">
                <h6>Part Hampir Habis (Stock mendekati Minimum)</h6>
                <table class="table table-bordered w-100" id="medium-table-{{ $loop->index }}">
                    <thead class="table-dark text-white">
                        <tr>
                            <th class="p-2">Nama Part</th>
                            <th class="p-2">Min</th>
                            <th class="p-2">Max</th>
                            <th class="p-2">Stock Tersisa</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($data['medium_parts'] as $part)
                        <tr class="{{ $part['status'] == 'warning' ? 'table-warning' : 'table-warning' }}">
                            <td>{{ $part['part_name'] }}</td>
                            <td>{{ $part['min_stock'] }}</td>
                            <td>{{ $part['max_stock'] }}</td>
                            <td>{{ $part['stock_quantity'] }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
                <div id="medium-pagination-{{ $loop->index }}" class="pagination-container mt-3">
                    <!-- Pagination will be rendered here by JavaScript -->
                </div>
            </div>
            @endif
        </div>
        @endif
        @endforeach
    </div>
    <div class="col pr-2 pl-2 ml-2 mr-2">
        <div class="row">
            <div class="col">
                <div class="">
                    <div class="m-0">
                        <div class="p-2 align-items-center shadow-kit bgwhite mb-1">
                            <div class="card-body">
                                <div class="d-flex align-items-start">
                                    <i class="text-danger m-2"></i>
                                    <div class="w-100">
                                        <small class="text-muted d-block mb-2">
                                            Tanggal: {{ \Carbon\Carbon::parse($startDate)->format('d-m-Y') }} s/d {{ \Carbon\Carbon::parse($endDate)->format('d-m-Y') }}
                                        </small>

                                        <!-- Total Berdasarkan PO -->
                                        <div class="mb-3">
                                            <h6 class="font-weight-bold text-primary mb-1">Total Berdasarkan PO</h6>
                                            <div>
                                                <small class="text-muted">Before TAX</small>
                                                <h5 class="h4 m-0 card-text font-weight-bold" id="totalUnitTransactionsbeforetax">
                                                    {{ $formattedTotalUnitTransactionsbeforetax }}
                                                </h5>
                                            </div>
                                            <div>
                                                <small class="text-muted">After TAX</small>
                                                <h4 class="h5 m-0 card-text font-weight-bold" id="totalUnitTransactions">
                                                    {{ $formattedTotalUnitTransactions }}
                                                </h4>
                                            </div>
                                        </div>

                                        <hr class="my-2">

                                        <!-- Total Berdasarkan MR -->
                                        <div>
                                            <h6 class="font-weight-bold text-primary mb-1">Total Berdasarkan MR</h6>
                                            <div>
                                                <small class="text-muted">Before TAX</small>
                                                <h5 class="h4 m-0 font-weight-bold" id="mrtotalUnitTransactionsbeforetax">
                                                    {{ $mrformattedTotalUnitTransactionsbeforetax }}
                                                </h5>
                                            </div>
                                            <div>
                                                <small class="text-muted">After TAX</small>
                                                <h4 class="h5 m-0 card-text font-weight-bold" id="mrtotalUnitTransactions">
                                                    {{ $mrformattedTotalUnitTransactions }}
                                                </h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="card-body bgwhite shadow-kit">
                        <div class="pb-0 d-flex align-items-center">
                            <i class="fas fa-arrow-alt-circle-down fa-3x text-success mr-3"></i> <!-- Ikon In-Stock -->
                            <div>
                                <h5 class="card-title font-weight-bold m-0">Total In-Stocks</h5>
                                <p class="card-text display-4 font-weight-bold mb-0" id="totalInStock">{{ $totalInStock }}</p>
                            </div>
                        </div>
                    </div>
                    <hr class="m-1 p-0">
                    <div class="card-body bgwhite shadow-kit">
                        <div class="pt-0 d-flex align-items-center">
                            <i class="fas fa-arrow-alt-circle-up fa-3x text-secondary mr-3"></i> <!-- Ikon Out-Stock -->
                            <div>
                                <h5 class="card-title font-weight-bold m-0">Total Out-Stocks</h5>
                                <!-- Add ID to this element -->
                                <p class="card-text display-4 font-weight-bold mb-0" id="totalOutStock">{{ $totalOutStock }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="bgwhite shadow-kit">
                    <h5 class="pt-3 pl-4 mt-0 text-uppercase font-bold"> Status Stock</h5>
                    @if(isset($inventoryData))
                    @foreach($inventoryData as $data)
                    <div class="p-0">
                        <div class="card-header">{{ $data['site_name'] }} - Inventory Status</div>
                        <div class="">
                            <canvas id="pieChart{{ Str::slug($data['site_name']) }}" width="300" height="350"></canvas>
                        </div>
                    </div>
                    @endforeach
                    @else
                    <p>No inventory data available.</p>
                    @endif
                </div>
            </div>
        </div>
        <div class="row p-1">
            <div class="col p-0">
                <div class="shadow-kit bgwhite p-4 mt-2">
                    <h3 class="font-bold h5 text-uppercase">Remainder Tugas dan Kebutuhan</h3>

                    <!-- Filter Section -->
                    <div class="row mb-3">
                        <div class="col-md-2 ml-2">
                            <input type="date" class="btn btn-sm btn-primary" id="remainderFilterStart" value="{{ date('Y-m-d') }}">
                        </div>
                        <div class="col-md-2 ml-3">
                            <input type="date" class="btn btn-sm btn-secondary" id="remainderFilterEnd" value="{{ date('Y-m-d', strtotime('+9 days')) }}">
                        </div>
                        {{-- <div class="col-md-4 d-flex align-items-end">
                            <button type="button" class="btn btn-primary" id="remainderFilterBtn">Filter</button>
                        </div> --}}
                    </div>

                    <!-- Table Section -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="remainderTaskTable">
                            <thead class="table-dark">
                                <tr>
                                    <th>Nama Unit</th>
                                    <th>Tanggal Service</th>
                                    <th>HM Service</th>
                                    <th>Sumber</th>
                                    <th>Daftar Part</th>
                                    <th>Stock Part</th>
                                </tr>
                            </thead>
                            <tbody id="remainderTaskTableBody">
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Loading indicator -->
                    <div id="remainderLoadingIndicator" class="text-center py-4" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Memuat data...</p>
                    </div>

                    <!-- Empty state -->
                    <div id="remainderEmptyState" class="text-center py-4" style="display: none;">
                        <p class="text-muted">Tidak ada data tugas dan kebutuhan dalam periode yang dipilih.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="exportModal" class="modal" tabindex="-1" role="dialog" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="font-bold text-uppercase" id="title">Filter Laporan</h5>
                <span class="close" aria-label="Close">&times;</span>
            </div>
            <form id="exportForm" method="GET">
                <input type="hidden" name="report_type" id="reportType">
                <div class="modal-body">
                    <div class="form-grid">
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="start_date">Tanggal Mulai</label>
                                <input type="date" class="form-control" name="start_date">
                            </div>
                            <div class="form-group flex-grow-1">
                                <label for="end_date">Tanggal Selesai</label>
                                <input type="date" class="form-control" name="end_date">
                            </div>
                        </div>
                        <div class="d-flex gap-3">
                            <div class="form-group flex-grow-1">
                                <label for="sort_by">Urutkan Berdasarkan</label>
                                <select class="custom-select" name="sort_by">
                                    <option value="date">Tanggal</option>
                                    <option value="part_name">Nama Part</option>
                                </select>
                            </div>
                            <div class="form-group flex-grow-1 hidden">
                                <label for="site_id">Site</label>
                                <select class="custom-select" name="site_id">
                                    <option value="{{ session('site_id') }}" selected>Semua Site</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <!-- <button type="button" class="btn btn-secondary" id="closeModal">Tutup</button> -->
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportPdf">
                        <i class="mdi mdi-file-pdf-box mr-1"></i> Export PDF
                    </button>
                    <button type="button" class="btn btn-pink btn-rounded waves-effect waves-light" id="exportExcel">
                        <i class="mdi mdi-file-excel mr-1"></i> Export Excel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for Remainder Task Details -->
<div class="modal fade" id="remainderDetailModal" tabindex="-1" aria-labelledby="remainderDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width: 90%; width: 50%;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="remainderDetailModalLabel">Detail Tugas dan Kebutuhan</h5>
                <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="remainderDetailModalBody">
                <!-- Content will be loaded via AJAX -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>
@endsection
@section('resourcesite')
@vite(['resources/js/site/Chartsite.js','resources/js/style.js','resources/js/piechart.js'])
<!-- Pagination Styling -->
<style>
    .pagination-container {
        display: flex;
        justify-content: center;
    }

    .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .pagination li {
        margin: 0 2px;
    }

    .pagination li a {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
        display: block;
    }

    .pagination li.active a {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .pagination li a:hover {
        background-color: #f8f9fa;
    }

    .pagination li.disabled a {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
        border-color: #ddd;
    }

    /* Remainder Tasks Styling */
    .remainder-task-row {
        transition: background-color 0.2s ease;
    }

    .remainder-task-row:hover {
        background-color: #f8f9fa !important;
    }

    #remainderTaskTable th {
        background-color: #343a40;
        color: white;
        font-weight: 600;
        text-align: center;
        vertical-align: middle;
    }

    #remainderTaskTable td {
        vertical-align: middle;
        text-align: center;
    }

    #remainderTaskTable .fw-bold {
        font-weight: 600;
    }

    .badge {
        font-size: 0.75em;
    }

    .spinner-border {
        width: 2rem;
        height: 2rem;
    }

    .table-responsive {
        border-radius: 0.375rem;
        overflow: hidden;
    }

    #remainderFilterBtn {
        height: 38px;
    }
</style>

@vite('resources/js/site/Dashboard.js')
@vite('resources/js/laporan.js')
@vite('resources/js/site/remainder-tasks.js')
<script>
    window.chartData = @json($chartData);
    window.groupBy = @json($groupBy);
    window.inventoryData = @json($inventoryData);

    // Pagination functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Function to create pagination for a table
        function setupPagination(tableId, paginationId, itemsPerPage = 5) {
            const table = document.getElementById(tableId);
            if (!table) return;

            const tbody = table.querySelector('tbody');
            const rows = tbody.querySelectorAll('tr');
            const totalPages = Math.ceil(rows.length / itemsPerPage);

            let currentPage = 1;

            // Function to show the appropriate rows for the current page
            function showPage(page) {
                const start = (page - 1) * itemsPerPage;
                const end = start + itemsPerPage;

                // Hide all rows
                rows.forEach((row, index) => {
                    row.style.display = (index >= start && index < end) ? '' : 'none';
                });

                // Update pagination UI
                updatePagination();
            }

            // Function to create pagination controls
            function updatePagination() {
                const paginationContainer = document.getElementById(paginationId);
                if (!paginationContainer) return;

                paginationContainer.innerHTML = '';

                if (totalPages <= 1) return; // Don't show pagination if only one page

                const ul = document.createElement('ul');
                ul.className = 'pagination pagination-rounded ';

                // Previous button
                const prevLi = document.createElement('li');
                prevLi.className = currentPage === 1 ? 'disabled' : '';
                const prevA = document.createElement('a');
                prevA.href = '#';
                prevA.textContent = '«';
                prevA.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage > 1) {
                        currentPage--;
                        showPage(currentPage);
                    }
                });
                prevLi.appendChild(prevA);
                ul.appendChild(prevLi);

                // Page numbers
                for (let i = 1; i <= totalPages; i++) {
                    const li = document.createElement('li');
                    li.className = i === currentPage ? 'active' : '';
                    const a = document.createElement('a');
                    a.href = '#';
                    a.textContent = i;
                    a.addEventListener('click', function(e) {
                        e.preventDefault();
                        currentPage = i;
                        showPage(currentPage);
                    });
                    li.appendChild(a);
                    ul.appendChild(li);
                }

                // Next button
                const nextLi = document.createElement('li');
                nextLi.className = currentPage === totalPages ? 'disabled' : '';
                const nextA = document.createElement('a');
                nextA.href = '#';
                nextA.textContent = '»';
                nextA.addEventListener('click', function(e) {
                    e.preventDefault();
                    if (currentPage < totalPages) {
                        currentPage++;
                        showPage(currentPage);
                    }
                });
                nextLi.appendChild(nextA);
                ul.appendChild(nextLi);

                paginationContainer.appendChild(ul);
            }

            // Initialize pagination
            showPage(currentPage);
        }

        // Setup pagination for all tables
        const inventoryData = window.inventoryData || [];
        inventoryData.forEach((data, index) => {
            if (data.not_ready_parts && data.not_ready_parts.length > 0) {
                setupPagination(`not-ready-table-${index}`, `not-ready-pagination-${index}`, 5);
            }
            if (data.medium_parts && data.medium_parts.length > 0) {
                setupPagination(`medium-table-${index}`, `medium-pagination-${index}`, 5);
            }
        });
    });
</script>
@endsection