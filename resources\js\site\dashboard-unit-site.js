/**
 * Dashboard Unit Site JavaScript
 * Handles AJAX functionality for unit dashboard
 */

class DashboardUnitSite {
    constructor() {
        this.selectedUnit = null;
        this.charts = {};
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeComponents();
    }

    bindEvents() {
        // Unit search events
        $("#unit-search-input").on("input", (e) => this.handleSearchInput(e));
        $("#unit-search-input").on("focus", (e) => this.handleSearchFocus(e));
        $("#unit-search-input").on("blur", (e) => this.handleSearchBlur(e));
        $("#unit-search-input").on("keydown", (e) =>
            this.handleSearchKeydown(e)
        );
        $(document).on("click", ".unit-search-result", (e) =>
            this.selectUnit(e)
        );
        $(document).on("mouseenter", ".unit-search-result", (e) =>
            this.highlightResult(e)
        );

        // Change unit button
        $("#change-unit-btn").on("click", () => this.showUnitSearch());

        // Daily report table row click
        $(document).on("click", ".daily-report-row", (e) =>
            this.showDailyReportDetail(e)
        );

        // Close search results when clicking outside
        $(document).on("click", (e) => {
            if (
                !$(e.target).closest(
                    "#unit-search-input, .unit-search-dropdown"
                ).length
            ) {
                this.hideSearchResults();
            }
        });

        // Prevent page scroll on modal interactions
        $(document).on("show.bs.modal", ".modal", function (e) {
            $("body").addClass("modal-open");
        });

        $(document).on("hidden.bs.modal", ".modal", function (e) {
            $("body").removeClass("modal-open");
        });
    }

    initializeComponents() {
        // Initialize tooltips
        if (typeof $ !== "undefined" && $.fn.tooltip) {
            $('[data-bs-toggle="tooltip"]').tooltip();
        }

        // Show empty state initially
        this.showEmptyState();
    }

    handleSearchInput(e) {
        const query = e.target.value.trim();

        // clearTimeout(this.searchTimeout);
        if (query.length >= 1) {
            this.showSearchLoading();

            // Debounce agar tidak trigger terlalu sering
            this.searchTimeout = setTimeout(() => {
                this.searchUnits(query);
            }, 150); // lebih cepat, agar terasa real-time
        } else {
            this.hideSearchResults();
            this.hideSearchLoading();
        }
    }

    handleSearchFocus(e) {
        const query = e.target.value.trim();
        if (query.length >= 1) {
            // Show existing results if available
            if ($("#unit-search-results .unit-search-result").length > 0) {
                this.showSearchResults();
            }
        }
    }

    handleSearchBlur(e) {
        // Delay hiding to allow clicking on results
        setTimeout(() => {
            if (!$("#unit-search-results:hover").length) {
                this.hideSearchResults();
            }
        }, 150);
    }

    async searchUnits(query) {
        try {
            const csrfToken = document
                .querySelector('meta[name="csrf-token"]')
                ?.getAttribute("content");
            if (!csrfToken) {
                throw new Error("CSRF token not found");
            }

            const response = await fetch("/dashboard-unit-site/search-units", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": csrfToken,
                },
                body: JSON.stringify({ query }),
            });

            this.hideSearchLoading();

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                this.displaySearchResults(data.data);
            } else {
                console.warn("Search failed:", data.message);
                this.displaySearchResults([]);
            }
        } catch (error) {
            console.error("Search error:", error);
            this.hideSearchLoading();
            this.displaySearchResults([]);
            this.showError("Terjadi kesalahan saat mencari unit");
        }
    }

    displaySearchResults(units) {
        const resultsContainer = $("#unit-search-results");

        if (units.length === 0) {
            resultsContainer.html(
                '<div class="no-results">Tidak ada unit ditemukan</div>'
            );
        } else {
            const html = units
                .map(
                    (unit) => `
                <div class="unit-search-result" data-unit-id="${unit.id}">
                    <div class="unit-code d-flex">${unit.unit_code} - <p class="my-1 mx-2" style="font-size: 11px !important;">${unit.unit_type}</p> </div>
                </div>`
                )
                .join("");
            resultsContainer.html(html);
        }
        this.showSearchResults();
    }

    showSearchResults() {
        $("#unit-search-results").show();
    }

    hideSearchResults() {
        $("#unit-search-results").hide();
    }

    showSearchLoading() {
        $("#search-loading").show();
        $(".search-icon").hide();
    }

    hideSearchLoading() {
        $("#search-loading").hide();
        $(".search-icon").show();
    }

    handleSearchKeydown(e) {
        const results = $(".unit-search-result");
        const highlighted = $(".unit-search-result.highlighted");

        switch (e.key) {
            case "ArrowDown":
                e.preventDefault();
                if (results.length > 0) {
                    if (highlighted.length === 0) {
                        results.first().addClass("highlighted");
                    } else {
                        const next = highlighted.next(".unit-search-result");
                        highlighted.removeClass("highlighted");
                        if (next.length > 0) {
                            next.addClass("highlighted");
                        } else {
                            results.first().addClass("highlighted");
                        }
                    }
                }
                break;

            case "ArrowUp":
                e.preventDefault();
                if (results.length > 0) {
                    if (highlighted.length === 0) {
                        results.last().addClass("highlighted");
                    } else {
                        const prev = highlighted.prev(".unit-search-result");
                        highlighted.removeClass("highlighted");
                        if (prev.length > 0) {
                            prev.addClass("highlighted");
                        } else {
                            results.last().addClass("highlighted");
                        }
                    }
                }
                break;

            case "Enter":
                e.preventDefault();
                if (highlighted.length > 0) {
                    this.selectUnit({
                        currentTarget: highlighted[0],
                        preventDefault: () => {},
                    });
                }
                break;

            case "Escape":
                this.hideSearchResults();
                const input = document.getElementById("unit-search-input");
                if (input) input.blur();
                break;
        }
    }

    highlightResult(e) {
        $(".unit-search-result").removeClass("highlighted");
        $(e.currentTarget).addClass("highlighted");
    }

    async selectUnit(e) {
        e.preventDefault();
        const unitId = $(e.currentTarget).data("unit-id");
        const unitCode = $(e.currentTarget).find(".unit-code").text();

        this.hideSearchResults();
        $("#unit-search-input").val(unitCode);

        await this.loadDashboardData(unitId);

        return false;
    }

    async loadDashboardData(unitId) {
        try {
            this.showLoading();

            const csrfToken = document
                .querySelector('meta[name="csrf-token"]')
                ?.getAttribute("content");
            if (!csrfToken) {
                throw new Error("CSRF token not found");
            }

            const response = await fetch(
                "/dashboard-unit-site/dashboard-data",
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": csrfToken,
                    },
                    body: JSON.stringify({ unit_id: unitId }),
                }
            );

            const data = await response.json();

            if (data.success) {
                this.selectedUnit = data.data.unit;
                this.renderDashboard(data.data);
                this.showDashboard();
            } else {
                this.showError("Gagal memuat data dashboard: " + data.message);
                this.showEmptyState();
            }
        } catch (error) {
            console.error("Dashboard load error:", error);
            this.showError("Terjadi kesalahan saat memuat dashboard");
            this.showEmptyState();
        }
    }

    renderDashboard(data) {
        // Update unit header
        $("#selected-unit-title").text(
            `${data.unit.unit_code} - ${data.unit.unit_model}`
        );
        $("#selected-unit-info").text(
            `${data.unit.unit_type} | Site: ${data.unit.site_id}`
        );

        // Render daily reports table
        this.renderDailyReportsTable(data.daily_reports);

        // Render latest report
        this.renderLatestReport(data.latest_report);

        // Render open backlogs
        this.renderOpenBacklogs(data.open_backlogs);

        // Render closed backlogs
        this.renderClosedBacklogs(data.closed_backlogs);

        // Render part usage stats
        this.renderPartUsageStats(data.part_usage_stats);

        // Render problem component analysis
        this.renderProblemComponentAnalysis(data.service_component_stats);

        // Render monthly usage chart
        this.renderMonthlyUsageChart(data.monthly_usage_stats);
    }

    renderDailyReportsTable(reports) {
        const tbody = $("#daily-reports-table tbody");

        if (reports.length === 0) {
            tbody.html(
                '<tr><td colspan="5" class="text-center text-muted">Tidak ada data daily report</td></tr>'
            );
            return;
        }

        const html = reports
            .map(
                (report) => `
            <tr class="daily-report-row" data-report-id="${report.daily_report_id}" style="cursor: pointer;">
                <td>${report.date_in}</td>
                <td>${report.hm}</td>
                <td>${report.problem}</td>
                <td>${report.shift || "-"}</td>
            </tr>`).join("");

        tbody.html(html);
    }

    formatDateTimeIndo(dateString) {
        if (!dateString) return "-";

        // Cek jika format dd/mm/yyyy, lalu ubah ke yyyy-mm-dd
        const parts = dateString.split("/");
        if (parts.length === 3) {
            // parts[2] = year, parts[1] = month, parts[0] = day
            dateString = `${parts[2]}-${parts[1]}-${parts[0]}`;
        }

        const date = new Date(dateString);
        if (isNaN(date)) return "-";

        const day = String(date.getDate()).padStart(2, "0");
        const month = date.toLocaleString("id-ID", { month: "long" });
        const year = date.getFullYear();

        return `${day} ${month} ${year}`;
    }

    renderLatestReport(report) {
        const container = $("#latest-report-content");

        if (!report) {
            container.html('<p class="text-muted">Tidak ada data report</p>');
            return;
        }

        const dtTimeText = report.dt_time
            ? `${report.dt_time} menit`
            : "Tidak tersedia";

        const html = `
            <div class="latest-report-card">
                <div class="row mb-3 m-0 bg-blue-100 p-3 rounded-lg hoversection">
                    <div class="col-4">
                        <small class="text-muted">Tanggal Services</small>
                        <div class="fw-bold">${report.date_in}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">HM</small>
                        <div class="fw-bold">${report.hm}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Problem</small>
                        <div><span class="fw-bold">${
                            report.problem
                        }</span></div>
                    </div>
                </div>
                
                <div class="row mb-3 m-0  bg-green-100 p-3 rounded-lg hoversection">
                    <div class="col-4">
                        <small class="text-muted">Jam Masuk</small>
                        <div>${report.hour_in || "-"}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Jam Keluar</small>
                        <div>${report.hour_out || "-"}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Downtime</small>
                        <div class="fw-bold text-primary">${dtTimeText}</div>
                    </div>
                </div>

                <div class="row mb-3 m-0  bg-red-100 p-3  rounded-lg hoversection">
                    <div class="col-6">
                        <small class="text-muted">Problem Komponen</small>
                        <div class="fw-bold">${report.problem_component}</div>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">Problem Deskripsi unit</small>
                        <div class="fw-bold">${report.problem_description}</div>
                    </div>
                </div>

                ${
                    report.problem_description
                        ? `
                <div class="row mb-3 m-0 hovertext">
                    <div class="col-12">
                        <small class="text-muted">Deskripsi</small>
                        <div class="text-wrap">${report.problem_description}</div>
                    </div>
                </div>`
                        : ""
                }

                <div class="row mb-3 m-0 hovertext">
                    <div class="col-6">
                        <small class="text-muted">Job Description (${
                            report.jobs.length
                        })</small>
                        <div class="mt-1">
                            ${report.jobs
                                .map((job) => `<p>- ${job}</p>`)
                                .join("")}
                        </div>
                    </div>
                    <div class="col-6 hovertext">
                    ${ report.technicians && report.technicians.length > 0 ? `
                    <small class="text-muted">Technicians (${
                        report.technicians.length
                    })</small>
                    <div class="mt-1">
                        ${report.technicians
                            .map((technician) => `<p>- ${technician}</p>`)
                            .join("")} </div>` : "" }
                    </div>
                </div>
                ${ report.Parts && report.Parts.length > 0 ? `
                    <div class="row mb-3 m-0 hovertext">
                        <div class="col-12">
                            <small class="text-muted">Parts :</small>
                            <div class="mt-1">
                                ${report.Parts.map(part =>
                                    `<p class="p-1">${part.part_name} - ${part.part_code}</p>`
                                ).join('')}
                            </div>
                        </div>
                    </div>
                ` : "" }
            </div>
        `;

        container.html(html);
    }

    renderOpenBacklogs(backlogs) {
        const container = $("#open-backlogs-content");

        if (backlogs.length === 0) {
            container.html('<p class="text-muted">Tidak ada backlog OPEN</p>');
            return;
        }
        const html = backlogs
            .map(
                (backlog) => `
            <div class="backlog-item mb-0 p-3 border rounded">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">Tanggal Pembuatan</h6>
                    <small class="text-muted">${backlog.created_at}</small>
                </div>
                <div class="row m-0 hovertext">
                    <div class="col-12">
                        <small class="text-muted">Backlog Job</small>
                         <p class="text-muted">${ backlog.problem_description }</p>
                         <p class="text-muted mb-3">${backlog.backlog_job}</p>
                    </div>
                </div>

                <div class="col-12">
                        <small class="text-muted">Catatan tambahan</small>
                        <div>${backlog.notes}</div>
                    </div>
                </div>

                <hr class="my-2">

                <div class="row m-0 hovertext">
                    <div class="col-4">
                        <small class="text-muted">HM Temuan</small>
                        <div>${backlog.hm_found || "-"}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Plan HM</small>
                        <div>${backlog.plan_hm || "-"}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Plan Pull Date</small>
                        <div>${backlog.plan_pull_date || "-"}</div>
                    </div>
                </div>

                ${
                    backlog.parts.length > 0
                        ? `
                <div class="mt-2">
                    <small class="text-muted">Parts :</small>
                    <div class="mt-1">
                        ${backlog.parts
                            .map(
                                (part) =>
                                    `<span class="supernatural badge bg-info p-1">${part.part_name} (${part.quantity})</span><br><p class="text-muted p-1">${part.part_code}</p>`
                            )
                            .join("")}
                    </div>
                </div>`
                        : ""
                } </div> `
            )
            .join("");

        container.html(html);
    }

    renderPartUsageStats(stats) {
        const container = $("#part-usage-content");

        if (stats.length === 0) {
            container.html(
                '<p class="text-muted">Tidak ada data penggunaan part</p>'
            );
            return;
        }

        const html = stats
            .map(
                (stat) => `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <div>
                <div class="fw-bold text-uppercase">${stat.part_name ?? '-'}</div>
                <small class="text-muted">${stat.part_code ?? '-'}</small>
                </div>
                <span class="badge bg-success">${stat.total_quantity ?? 0} X</span>
            </div>
        `
            )
            .join("");

        container.html(html);
    }

    renderClosedBacklogs(backlogs) {
        const container = $("#closed-backlogs-content");

        if (backlogs.length === 0) {
            container.html(
                '<p class="text-muted">Tidak ada backlog CLOSED</p>'
            );
            return;
        }

        const html = backlogs
            .map(
                (backlog) => `
            <div class="backlog-item mb-0 p-3 border rounded">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">Tanggal Pembuatan</h6>
                    <small class="text-muted">${backlog.created_at ?? '-'}</small>
                </div>
                <div class="row m-0 hovertext">
                    <div class="col-12">
                        <small class="text-muted">Backlog Job</small>
                         <p class="text-muted mb-2">${
                             backlog.problem_description ?? 'Tidak ada deskripsi'
                         }</p>
                    </div>
                </div>
                <div class="row m-0 hovertext">
                <div class="col-12">
                        <small class="text-muted">Backlog Job</small>
                        <div>${backlog.backlog_job ?? '-'}</div>
                    </div>
                </div>

                <hr class="my-2">

                <div class="row m-0 hovertext">
                    <div class="col-4">
                        <small class="text-muted">HM Temuan</small>
                        <div>${backlog.hm_found || "-"}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Plan HM</small>
                        <div>${backlog.plan_hm || "-"}</div>
                    </div>
                    <div class="col-4">
                        <small class="text-muted">Plan Pull Date</small>
                        <div>${backlog.plan_pull_date || "-"}</div>
                    </div>
                </div>

                ${ backlog.parts.length > 0 ? `
                <div class="mt-2">
                    <small class="text-muted">Parts :</small>
                    <div class="mt-1">
                        ${backlog.parts
                            .map(
                                (part) =>
                                    `<span class="supernatural badge bg-danger p-1">${part.part_name} (${part.quantity})</span><br><p class="text-muted p-1">${part.part_code}</p>`
                            )
                            .join("")}
                    </div>
                </div>` : ""
                } </div> `
            )
            .join("");

        container.html(html);
    }

    renderProblemComponentAnalysis(data) {
        const container = $("#problem-component-content");

        if (data.length === 0) {
            container.html(
                '<p class="text-muted">Belum ada data komponen masalah</p>'
            );
            return;
        }

        const labels = data.map((item) => item.part_name);
        const quantities = data.map((item) => item.quantity);

        // Generate consistent unique colors
        const colors = labels.map((label) => stringToColor(label));

        const canvasId = "problemComponentChart";
        const legendId = "problemComponentLegend";

        const html = `
        <p class="mb-2"><strong>Komponen Unscheduled Services:</strong></p>
        <canvas id="${canvasId}" height="300"></canvas>
        <ul id="${legendId}" class="mt-3 list-unstyled"></ul>
    `;

        container.html(html);

        const ctx = document.getElementById(canvasId).getContext("2d");
        const chart = new Chart(ctx, {
            type: "bar",
            data: {
                labels: labels,
                datasets: [
                    {
                        label: "Total Quantity",
                        data: quantities,
                        backgroundColor: colors,
                        borderColor: colors,
                        borderWidth: 1,
                    },
                ],
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                return `${context.label}: ${context.raw}`;
                            },
                        },
                    },
                    legend: {
                        // display: false,
                    },
                },
                scales: {
                    x: {
                        ticks: {
                            callback: function (value) {
                                const fullLabel = this.getLabelForValue(value);
                                return fullLabel.length > 10
                                    ? fullLabel.substring(0, 10) + "…"
                                    : fullLabel;
                            },
                            font: {
                                style: "italic",
                                size: 10,
                            },
                            rotation: 90,
                        },
                    },
                    y: {
                        beginAtZero: true,
                    },
                },
            },
        });

        // Render legend list with toggle
        const legendContainer = $(`#${legendId}`);
        labels.forEach((label, index) => {
            const color = colors[index];
            const shortLabel =
                label.length > 60 ? label.substring(0, 20) + "…" : label;

            const li = $(`
            <li class="mb-1" style="cursor: pointer; display: flex; align-items: center;">
                <span class="legend-color-box me-2" 
                    style="display: inline-block; width: 15px; height: 10px; background-color: ${color}; border-radius: 3px;">
                </span>
                <span style="color: ${color}; font-weight: 500;">${shortLabel}</span>
            </li>
        `);

            li.on("click", function () {
                const meta = chart.getDatasetMeta(0);
                const item = meta.data[index];
                item.hidden = !item.hidden; // Toggle visibility
                chart.update();
            });

            legendContainer.append(li);
        });

        // Helper: consistent color generator
        function stringToColor(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                hash = str.charCodeAt(i) + ((hash << 50) - hash);
            }
            const h = hash % 360;
            return `hsl(${h}, 65%, 55%)`;
        }
    }

    renderServiceComponentChart(data) {
        const ctx = document.getElementById("service-component-chart");

        // Destroy existing chart
        if (this.charts.serviceComponent) {
            if (window.DashboardChartUtils) {
                window.DashboardChartUtils.destroyChart(
                    this.charts.serviceComponent
                );
            } else if (this.charts.serviceComponent.destroy) {
                this.charts.serviceComponent.destroy();
            }
        }

        if (data.length === 0) {
            if (window.DashboardChartUtils) {
                window.DashboardChartUtils.clearCanvas(ctx);
            } else if (ctx) {
                const context = ctx.getContext("2d");
                context.clearRect(0, 0, ctx.width, ctx.height);
            }
            return;
        }

        if (window.DashboardChartUtils) {
            this.charts.serviceComponent =
                window.DashboardChartUtils.createDoughnutChart(ctx, data);
        } else {
            console.warn(
                "DashboardChartUtils not available, falling back to basic Chart.js"
            );
            // Fallback implementation
            this.charts.serviceComponent = new window.Chart(ctx, {
                type: "doughnut",
                data: {
                    labels: data.map((item) => item.component),
                    datasets: [
                        {
                            data: data.map((item) => item.count),
                            backgroundColor: [
                                "#FF6384",
                                "#36A2EB",
                                "#FFCE56",
                                "#4BC0C0",
                                "#9966FF",
                                "#FF9F40",
                            ],
                        },
                    ],
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                },
            });
        }
    }

    renderMonthlyUsageChart(data) {
        const ctx = document.getElementById("monthly-usage-chart");

        // Destroy existing chart
        if (this.charts.monthlyUsage) {
            if (window.DashboardChartUtils) {
                window.DashboardChartUtils.destroyChart(
                    this.charts.monthlyUsage
                );
            } else if (this.charts.monthlyUsage.destroy) {
                this.charts.monthlyUsage.destroy();
            }
        }

        if (data.length === 0) {
            if (window.DashboardChartUtils) {
                window.DashboardChartUtils.clearCanvas(ctx);
            } else if (ctx) {
                const context = ctx.getContext("2d");
                context.clearRect(0, 0, ctx.width, ctx.height);
            }
            return;
        }

        if (window.DashboardChartUtils) {
            this.charts.monthlyUsage =
                window.DashboardChartUtils.createLineChart(ctx, data);
        } else {
            console.warn(
                "DashboardChartUtils not available, falling back to basic Chart.js"
            );
            // Fallback implementation
            this.charts.monthlyUsage = new window.Chart(ctx, {
                type: "line",
                data: {
                    labels: data.map((item) => item.period),
                    datasets: [
                        {
                            label: "Jumlah Report",
                            data: data.map((item) => item.count),
                            borderColor: "#36A2EB",
                            backgroundColor: "rgba(54, 162, 235, 0.1)",
                            tension: 0.4,
                            fill: true,
                        },
                    ],
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: { stepSize: 1 },
                        },
                    },
                    plugins: {
                        legend: { display: false },
                    },
                },
            });
        }
    }

    async showDailyReportDetail(e) {
        e.preventDefault();
        const reportId = $(e.currentTarget).data("report-id");

        try {
            const csrfToken = document
                .querySelector('meta[name="csrf-token"]')
                ?.getAttribute("content");
            if (!csrfToken) {
                throw new Error("CSRF token not found");
            }

            const response = await fetch(
                "/dashboard-unit-site/daily-report-detail",
                {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        "X-CSRF-TOKEN": csrfToken,
                    },
                    body: JSON.stringify({ report_id: reportId }),
                }
            );
            
            const data = await response.json();
            console.log(data.data);
            if (data.success) {
                // this.renderDailyReportModal(data.data);
                // render ke daily report detail
                this.renderLatestReport(data.data);
            } else {
                this.showError("Gagal memuat detail report: " + data.message);
            }
        } catch (error) {
            console.error("Report detail error:", error);
            this.showError("Terjadi kesalahan saat memuat detail report");
        }

        return false;
    }

    renderDailyReportModal(report) {
        const dtTimeText = report.dt_time
            ? `${report.dt_time} menit`
            : "Tidak tersedia";

        const html = `
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">Unit Code</label>
                    <div class="form-control-plaintext">${
                        report.unit_code || "-"
                    }</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Tanggal</label>
                    <div class="form-control-plaintext">${report.date_in || "-"}</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">HM</label>
                    <div class="form-control-plaintext">${report.hm || "-"}</div>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Shift</label>
                    <div class="form-control-plaintext">${
                        report.shift || "-"
                    }</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Jam Masuk</label>
                    <div class="form-control-plaintext">${
                        report.hour_in || "-"
                    }</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Jam Keluar</label>
                    <div class="form-control-plaintext">${
                        report.hour_out || "-"
                    }</div>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Downtime</label>
                    <div class="form-control-plaintext text-warning fw-bold">${dtTimeText}</div>
                </div>
                <div class="col-12 mb-3">
                    <label class="form-label">Problem</label>
                    <div class="form-control-plaintext">
                        <span class="text-mute fw-bold">${report.problem}</span>
                    </div>
                </div>
                ${ report.problem_component ? `
                <div class="col-12 mb-3">
                    <label class="form-label">Problem Component</label>
                    <div class="form-control-plaintext">${report.problem_component}</div>
                </div> `: "" }
                ${ report.problem_description ? `
                <div class="col-12 mb-3">
                    <label class="form-label">Problem Description</label>
                    <div class="form-control-plaintext">${report.problem_description}</div>
                </div> ` : "" }
                <div class="col-12 mb-3">
                    <label class="form-label">Jobs</label>
                    <div class="form-control-plaintext">
                        ${report.jobs
                            .map( (job) => ` <span class="badge bg-secondary me-1 mb-1">${job.job_description}</span>`).join("")}
                    </div>
                </div>
                ${
                    report.plan_fix
                        ? `
                <div class="col-12 mb-3">
                    <label class="form-label">Plan Fix</label>
                    <div class="form-control-plaintext">${report.plan_fix}</div>
                </div>
                `
                        : ""
                }
                ${
                    report.plan_rekomen
                        ? `
                <div class="col-12 mb-3">
                    <label class="form-label">Plan Rekomendasi</label>
                    <div class="form-control-plaintext">${report.plan_rekomen}</div>
                </div>
                `
                        : ""
                }
            </div>
        `;

        $("#daily-report-modal-content").html(html);
    }

    showUnitSearch() {
        this.showEmptyState();
        const input = document.getElementById("unit-search-input");
        if (input) {
            input.value = "";
            input.focus();
        }
    }

    showLoading() {
        $("#empty-state").hide();
        $("#dashboard-content").hide();
        $("#loading-state").show();
    }

    showEmptyState() {
        $("#loading-state").hide();
        $("#dashboard-content").hide();
        $("#empty-state").show();
    }

    showDashboard() {
        $("#loading-state").hide();
        $("#empty-state").hide();
        $("#dashboard-content").show();
    }

    getProblemBadgeColor(problem) {
        switch (problem?.toLowerCase()) {
            case "schedule":
                return "success";
            case "unschedule":
                return "danger";
            case "hm":
                return "warning";
            default:
                return "secondary";
        }
    }

    showError(message) {
        console.error("Dashboard error:", message);

        // Try to use a more modern notification if available
        if (window.Swal) {
            window.Swal.fire({
                icon: "error",
                title: "Error",
                text: message,
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
            });
        } else if (window.toastr) {
            window.toastr.error(message);
        } else {
            // Fallback to alert
            alert(message);
        }
    }
}

// Initialize when document is ready
document.addEventListener("DOMContentLoaded", function () {
    try {
        new DashboardUnitSite();
    } catch (error) {
        console.error("Failed to initialize Dashboard Unit Site:", error);
    }
});
