/**
 * Dashboard Unit Site Chart JavaScript
 * Handles Chart.js functionality for unit dashboard
 */

// Chart.js is loaded via CDN in the view, so we use the global Chart object

// Chart configuration and utilities
window.DashboardChartUtils = {
    // Default colors for charts
    colors: [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
        '#FF9F40', '#4BC0C0', '#9966FF', '#36A2EB'
    ],

    // Create doughnut chart
    createDoughnutChart(ctx, data, options = {}) {
        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12
                        }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label || '';
                            const value = context.parsed;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${label}: ${value} (${percentage}%)`;
                        }
                    }
                }
            }
        };

        return new window.Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.map(item => item.component || item.label),
                datasets: [{
                    data: data.map(item => item.count || item.value),
                    backgroundColor: this.colors.slice(0, data.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: { ...defaultOptions, ...options }
        });
    },

    // Create line chart
    createLineChart(ctx, data, options = {}) {
        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                        font: {
                            size: 11
                        }
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    ticks: {
                        font: {
                            size: 11
                        }
                    },
                    grid: {
                        display: false
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#36A2EB',
                    borderWidth: 1
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        };

        return new window.Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => item.period || item.label),
                datasets: [{
                    label: 'Jumlah Report',
                    data: data.map(item => item.count || item.value),
                    borderColor: '#36A2EB',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointBackgroundColor: '#36A2EB',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: { ...defaultOptions, ...options }
        });
    },

    // Create bar chart
    createBarChart(ctx, data, options = {}) {
        const defaultOptions = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                        font: {
                            size: 11
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            size: 11
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff'
                }
            }
        };

        return new window.Chart(ctx, {
            type: 'bar',
            data: {
                labels: data.map(item => item.label),
                datasets: [{
                    data: data.map(item => item.value),
                    backgroundColor: this.colors.slice(0, data.length),
                    borderWidth: 1,
                    borderColor: '#fff'
                }]
            },
            options: { ...defaultOptions, ...options }
        });
    },

    // Destroy chart safely
    destroyChart(chart) {
        if (chart && typeof chart.destroy === 'function') {
            chart.destroy();
        }
    },

    // Clear canvas
    clearCanvas(ctx) {
        if (ctx && ctx.getContext) {
            const context = ctx.getContext('2d');
            context.clearRect(0, 0, ctx.width, ctx.height);
        }
    },

    // Format number with thousand separator
    formatNumber(num) {
        return new Intl.NumberFormat('id-ID').format(num);
    },

    // Generate random color
    generateColor(index) {
        if (index < this.colors.length) {
            return this.colors[index];
        }
        
        // Generate random color if we run out of predefined colors
        const hue = (index * 137.508) % 360; // Golden angle approximation
        return `hsl(${hue}, 70%, 60%)`;
    }
};

// Initialize Chart.js defaults when Chart is available
document.addEventListener('DOMContentLoaded', function() {
    if (window.Chart) {
        window.Chart.defaults.font.family = "'Nunito', sans-serif";
        window.Chart.defaults.font.size = 12;
        window.Chart.defaults.color = '#6c757d';
    }
});

console.log('Dashboard Unit Site Chart utilities loaded');
