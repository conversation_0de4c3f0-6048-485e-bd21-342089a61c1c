<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Site;
use App\Models\SiteInStock;
use App\Models\SiteOutStock;
use App\Models\PartInventory;
use App\Models\Backlog;
use App\Models\DailyReport;
use App\Models\Unit;
use App\Models\Part;
use App\Models\BacklogPart;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardsiteContoller extends Controller
{
    public function index(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month'
        ]);
        $groupBy = $request->input('group_by', 'week');
        $today = Carbon::now();
        $defaultEndDate = $today->toDateString();
        if ($today->day === 1) {
            $defaultStartDate = $today->subMonth()->startOfMonth()->toDateString();
        } else {
            $defaultStartDate = $today->startOfMonth()->toDateString();
        }

        $startDate = $request->input('start_date', $defaultStartDate);
        $endDate = $request->input('end_date', $defaultEndDate);
        $siteId = session('site_id');
        $site = Site::find($siteId);

        if (!$site) {
            return redirect()->back()->with('error', 'Site not found.');
        }

        $periods = [];
        $currentDate = Carbon::parse($startDate);
        $endDateObj = Carbon::parse($endDate);
        while ($currentDate <= $endDateObj) {
            switch ($groupBy) {
                case 'day':
                    $periodStart = $currentDate->copy()->startOfDay();
                    $periodEnd = $currentDate->copy()->endOfDay();
                    $label = $currentDate->format('d M Y');
                    $currentDate->addDay();
                    break;
                case 'week':
                    $periodStart = $currentDate->copy()->startOfWeek();
                    $periodEnd = $currentDate->copy()->endOfWeek();
                    $periodEnd = $periodEnd->min($endDateObj);
                    $label = $periodStart->format('d M') . ' - ' . $periodEnd->format('d M');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
                case 'month':
                    $periodStart = $currentDate->copy()->startOfMonth();
                    $periodEnd = $currentDate->copy()->endOfMonth();
                    $periodEnd = $periodEnd->min($endDateObj);
                    $label = $periodStart->format('M Y');
                    $currentDate = $periodEnd->copy()->addDay();
                    break;
            }

            $periods[] = [
                'start' => $periodStart,
                'end' => $periodEnd,
                'label' => $label
            ];
        }

        $inStockData = [];
        $outStockData = [];

        // Inisialisasi variabel total
        $totalInStock = 0;
        $totalOutStock = 0;

        foreach ($periods as $period) {
            // In Stock Calculation
            $inStock = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->where('part_inventories.site_id', $site->site_id)
                ->whereBetween('date_in', [$period['start'], $period['end']])
                ->sum('site_in_stocks.quantity');
            $totalInStock += $inStock;

            // Out Stock Calculation
            $outStock = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->where('part_inventories.site_id', $site->site_id)
                ->whereBetween('date_out', [$period['start'], $period['end']])
                ->sum('site_out_stocks.quantity');

            $totalOutStock += $outStock;

            $inStockData[] = round($inStock, 2);
            $outStockData[] = round($outStock, 2);
        }

        $chartData = [
            'site_name' => $site->site_name,
            'labels' => array_column($periods, 'label'),
            'in_stock_data' => $inStockData,
            'out_stock_data' => $outStockData
        ];

        $inventoryData = $this->getInventoryStatusData(session('site_id'));

        // Round total values to 2 decimal places
        $totalInStock = round($totalInStock, 2);
        $totalOutStock = round($totalOutStock, 2);

        // Calculate total unit transactions based on PO date
        $unitTransactions = \App\Models\UnitTransaction::with(['parts'])
            ->where('site_id', session('site_id'))
            ->whereBetween('po_date', [
                $startDate . ' 00:00:00',
                $endDate . ' 23:59:59'
            ])->get();

        // Calculate total based on formula: (total all parts price) * 0.11 + total price
        $totalUnitTransactions = 0;
        $totalUnitTransactions2 = 0;

        foreach ($unitTransactions as $transaction) {
            // Calculate total price for all parts in this transaction
            $transactionTotal = 0;
            foreach ($transaction->parts as $part) {
                $price = $part->price ?? $part->partInventory->price;
                // Sum up all part prices * quantities
                $transactionTotal += $price * $part->quantity;
            }

            // Apply formula: (total price * 0.11) + total price
            $totalUnitTransactions2 += $transactionTotal;
            $totalUnitTransactions += ($transactionTotal * 0.11) + $transactionTotal;
        }


        // Calculate total unit transactions based on MR date
        $unitTransactions2 = \App\Models\UnitTransaction::with(['parts'])
            ->where('site_id', session('site_id'))
            ->whereBetween('mr_date', [
                $startDate . ' 00:00:00',
                $endDate . ' 23:59:59'
            ])->get();

        // Calculate total based on formula: (total all parts price) * 0.11 + total price
        $mrtotalUnitTransactions = 0;
        $mrtotalUnitTransactions2 = 0;

        foreach ($unitTransactions2 as $transaction) {
            // Calculate total price for all parts in this transaction
            $transactionTotal = 0;
            foreach ($transaction->parts as $part) {
                // Get price from part inventory (site-specific) instead of unit transaction part
                $price = $part->price ?? $part->partInventory->price;
                // Sum up all part prices * quantities
                $transactionTotal += $price * $part->quantity;
            }

            // Apply formula: (total price * 0.11) + total price
            $mrtotalUnitTransactions2 += $transactionTotal;
            $mrtotalUnitTransactions += ($transactionTotal * 0.11) + $transactionTotal;
        }

        // Format the total with Rupiah
        $formattedTotalUnitTransactions = 'Rp ' . number_format($totalUnitTransactions, 0, ',', '.');
        $formattedTotalUnitTransactionsbeforetax = 'Rp ' . number_format($totalUnitTransactions2, 0, ',', '.');
        $mrformattedTotalUnitTransactions = 'Rp ' . number_format($mrtotalUnitTransactions, 0, ',', '.');
        $mrformattedTotalUnitTransactionsbeforetax = 'Rp ' . number_format($mrtotalUnitTransactions2, 0, ',', '.');

        // Menambahkan total in dan out stock ke compact
        return view('sites.dashboard', compact(
            'chartData',
            'startDate',
            'endDate',
            'groupBy',
            'inventoryData',
            'totalInStock', // Pass total in stock
            'totalOutStock', // Pass total out stock
            'unitTransactions', // Pass unit transactions
            'formattedTotalUnitTransactions', // Pass formatted total unit transactions
            'formattedTotalUnitTransactionsbeforetax', // Pass formatted total unit transactions
            'mrformattedTotalUnitTransactions', // Pass formatted total unit transactions
            'mrformattedTotalUnitTransactionsbeforetax' // Pass formatted total unit transactions
        ));
    }

    private function getInventoryStatusData($siteId)
    {
        $site = Site::find($siteId);
        $inventoryData = [];
        if ($site) {
            $inventories = PartInventory::where('site_id', $siteId)->get();

            // Filter inventories to only include parts with min_stock != 0 for status monitoring
            // $monitoredInventories = $inventories->filter(function ($inventory) {
            //     return $inventory->min_stock != 0;
            // });
            $monitoredInventories = $inventories;
            $totalMonitoredInventories = $monitoredInventories->count();
            $notReadyCount = 0;
            $mediumCount = 0;
            $readyCount = 0;
            $notReadyParts = [];
            $mediumParts = [];

            foreach ($monitoredInventories as $inventory) {
                $averageStock = ($inventory->min_stock + $inventory->max_stock) / 2;
                // Updated Stock Status Logic
                // Part Not Ready: stock < average AND (min_stock != 0 OR max_stock != 0)
                if ($inventory->stock_quantity < $averageStock && $inventory->min_stock > 0 && $inventory->max_stock > 0) {
                    $status = 'danger';
                    $notReadyCount++;
                    $notReadyParts[] = [
                        'part_name' => $inventory->part->part_name,
                        'min_stock' => $inventory->min_stock,
                        'max_stock' => $inventory->max_stock,
                        'stock_quantity' => $inventory->stock_quantity,
                        'status' => $status,
                    ];
                } elseif ($inventory->stock_quantity >= $averageStock && $inventory->stock_quantity <= $inventory->max_stock) {
                    $status = 'warning';
                    $mediumCount++;
                    $mediumParts[] = [
                        'part_name' => $inventory->part->part_name,
                        'min_stock' => $inventory->min_stock,
                        'max_stock' => $inventory->max_stock,
                        'stock_quantity' => $inventory->stock_quantity,
                        'status' => $status,
                    ];
                } else {
                    $status = 'ok';
                }
                if ($inventory->stock_quantity >= $inventory->max_stock && $inventory->min_stock > 0 && $inventory->max_stock > 0) {
                    $readyCount++;
                }

            }
            $totalMonitoredInventories = $notReadyCount + $readyCount;
            $readyPercentage = round(($readyCount / $totalMonitoredInventories) * 100, 0);
            $notReadyPercentage = round(($notReadyCount / $totalMonitoredInventories) * 100, 0);
            $inventoryData[] = [
                'site_name' => $site->site_name,
                'ready_percentage' => $readyPercentage,
                'not_ready_percentage' => $notReadyPercentage,
                'not_ready_count' => $notReadyCount,
                'medium_count' => $mediumCount,
                'not_ready_parts' => $notReadyParts,
                'medium_parts' => $mediumParts,
                'total_monitored' => $totalMonitoredInventories, // Add for debugging/reference
            ];
        }

        return $inventoryData;
    }

    public function getStockTotals(Request $request)
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month'
        ]);

        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $groupBy = $request->input('group_by', 'week');
        $siteId = session('site_id');
        $site = Site::find($siteId);

        if (!$site) {
            return response()->json(['error' => 'Site not found.'], 404);
        }

        // In Stock Calculation
        $totalInStock = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->where('part_inventories.site_id', $site->site_id)
            ->whereBetween('date_in', [$startDate, $endDate])
            ->sum('site_in_stocks.quantity');

        // Out Stock Calculation
        $totalOutStock = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
            ->where('part_inventories.site_id', $site->site_id)
            ->whereBetween('date_out', [$startDate, $endDate])
            ->sum('site_out_stocks.quantity');

        // Calculate total unit transactions based on po date
        $unitTransactions = \App\Models\UnitTransaction::with(['parts'])
            ->where('site_id', $siteId)
            ->whereBetween('po_date', [$startDate . ' 00:00:00', $endDate . ' 23:59:59'])
            ->get();

        // Calculate total based on formula: (total all parts price) * 0.11 + total price
        $totalUnitTransactions = 0;
        $totalUnitTransactions2 = 0;
        foreach ($unitTransactions as $transaction) {
            // Calculate total price for all parts in this transaction
            $transactionTotal = 0;
            foreach ($transaction->parts as $part) {
                // Sum up all part prices * quantities
                $transactionTotal += $part->price * $part->quantity;
            }
            // Apply formula: (total price * 0.11) + total price
            $totalUnitTransactions += ($transactionTotal * 0.11) + $transactionTotal;
            $totalUnitTransactions2 +=  $transactionTotal;
        }


        // Calculate total unit transactions based on MR date
        $unitTransactions2 = \App\Models\UnitTransaction::with(['parts'])
            ->where('site_id', session('site_id'))
            ->whereBetween('mr_date', [
                $startDate . ' 00:00:00',
                $endDate . ' 23:59:59'
            ])->get();

        // Calculate total based on formula: (total all parts price) * 0.11 + total price
        $mrtotalUnitTransactions = 0;
        $mrtotalUnitTransactions2 = 0;

        foreach ($unitTransactions2 as $transaction) {
            // Calculate total price for all parts in this transaction
            $transactionTotal = 0;
            foreach ($transaction->parts as $part) {
                // Get price from part inventory (site-specific) instead of unit transaction part
                $price = $part->price ?? $part->partInventory->price;
                // Sum up all part prices * quantities
                $transactionTotal += $price * $part->quantity;
            }

            // Apply formula: (total price * 0.11) + total price
            $mrtotalUnitTransactions2 += $transactionTotal;
            $mrtotalUnitTransactions += ($transactionTotal * 0.11) + $transactionTotal;
        }


        // Format the total with Rupiah
        $formattedTotalUnitTransactions = 'Rp ' . number_format($totalUnitTransactions, 0, ',', '.');
        $formattedTotalUnitTransactionsbeforetax = 'Rp ' . number_format($totalUnitTransactions2, 0, ',', '.');
        $mrformattedTotalUnitTransactions = 'Rp ' . number_format($mrtotalUnitTransactions, 0, ',', '.');
        $mrformattedTotalUnitTransactionsbeforetax = 'Rp ' . number_format($mrtotalUnitTransactions2, 0, ',', '.');

        // Generate chart data based on group_by
        $periods = [];
        $inStockData = [];
        $outStockData = [];

        $startDateTime = \Carbon\Carbon::parse($startDate);
        $endDateTime = \Carbon\Carbon::parse($endDate);

        // Generate periods based on groupBy
        if ($groupBy === 'day') {
            for ($date = $startDateTime->copy(); $date->lte($endDateTime); $date->addDay()) {
                $periods[] = [
                    'start' => $date->copy()->startOfDay()->toDateTimeString(),
                    'end' => $date->copy()->endOfDay()->toDateTimeString(),
                    'label' => $date->format('d M')
                ];
            }
        } elseif ($groupBy === 'week') {
            $startWeek = $startDateTime->copy()->startOfWeek();
            for ($date = $startWeek; $date->lte($endDateTime); $date->addWeek()) {
                $weekEnd = $date->copy()->endOfWeek();
                if ($weekEnd->gt($endDateTime)) {
                    $weekEnd = $endDateTime->copy();
                }
                $periods[] = [
                    'start' => $date->copy()->toDateTimeString(),
                    'end' => $weekEnd->copy()->toDateTimeString(),
                    'label' => 'Week ' . $date->weekOfYear
                ];
            }
        } else { // month
            $startMonth = $startDateTime->copy()->startOfMonth();
            for ($date = $startMonth; $date->lte($endDateTime); $date->addMonth()) {
                $monthEnd = $date->copy()->endOfMonth();
                if ($monthEnd->gt($endDateTime)) {
                    $monthEnd = $endDateTime->copy();
                }
                $periods[] = [
                    'start' => $date->copy()->toDateTimeString(),
                    'end' => $monthEnd->copy()->toDateTimeString(),
                    'label' => $date->format('M Y')
                ];
            }
        }

        // Calculate data for each period
        foreach ($periods as $period) {
            // In Stock Calculation
            $inStock = SiteInStock::join('part_inventories', 'site_in_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->where('part_inventories.site_id', $site->site_id)
                ->whereBetween('date_in', [$period['start'], $period['end']])
                ->sum('site_in_stocks.quantity');

            // Out Stock Calculation
            $outStock = SiteOutStock::join('part_inventories', 'site_out_stocks.part_inventory_id', '=', 'part_inventories.part_inventory_id')
                ->where('part_inventories.site_id', $site->site_id)
                ->whereBetween('date_out', [$period['start'], $period['end']])
                ->sum('site_out_stocks.quantity');

            $inStockData[] = round($inStock, 2);
            $outStockData[] = round($outStock, 2);
        }

        $chartData = [
            'site_name' => $site->site_name,
            'labels' => array_column($periods, 'label'),
            'in_stock_data' => $inStockData,
            'out_stock_data' => $outStockData
        ];

        // Get inventory status data
        $inventoryData = $this->getInventoryStatusData(session('site_id'));

        return response()->json([
            'totalInStock' => round($totalInStock, 2),
            'totalOutStock' => round($totalOutStock, 2),
            'totalUnitTransactions' => $totalUnitTransactions,
            'formattedTotalUnitTransactions' => $formattedTotalUnitTransactions,
            'formattedTotalUnitTransactionsbeforetax' => $formattedTotalUnitTransactionsbeforetax,
            'mrformattedTotalUnitTransactions' => $mrformattedTotalUnitTransactions,
            'mrformattedTotalUnitTransactionsbeforetax' => $mrformattedTotalUnitTransactionsbeforetax,
            'dateRange' => [
                'start' => \Carbon\Carbon::parse($startDate)->format('d-m-Y'),
                'end' => \Carbon\Carbon::parse($endDate)->format('d-m-Y')
            ],
            'chartData' => $chartData,
            'inventoryData' => $inventoryData
        ]);
    }

    /**
     * Get remainder tasks and requirements data
     */
    public function getRemainderTasksData(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');
        $siteId = session('site_id');

        if (!$siteId) {
            return response()->json([
                'success' => false,
                'message' => 'Site ID not found in session'
            ], 400);
        }

        try {
            $remainderTasks = [];

            // 1. Get backlogs within selected date range
            $backlogs = Backlog::with(['unit', 'backlogParts.part'])
                ->whereHas('unit', function($query) use ($siteId) {
                    $query->where('site_id', $siteId);
                })
                ->where('status', 'OPEN')
                ->where(function($query) use ($startDate, $endDate) {
                    // If plan_pull_date exists, filter by date range
                    $query->whereBetween('plan_pull_date', [
                        $startDate . ' 00:00:00',
                        $endDate . ' 23:59:59'
                    ])
                    // If plan_pull_date is null, we'll calculate based on hm_found later
                    ->orWhereNull('plan_pull_date');
                })
                ->get();

            foreach ($backlogs as $backlog) {
                $parts = [];
                foreach ($backlog->backlogParts as $backlogPart) {
                    if ($backlogPart->part) {
                        // Get stock from inventory
                        $inventory = PartInventory::where('part_code', $backlogPart->part_code)
                            ->where('site_id', $siteId)
                            ->first();

                        $parts[] = [
                            'part_code' => $backlogPart->part_code,
                            'part_name' => $backlogPart->part->part_name,
                            'quantity' => $backlogPart->quantity,
                            'stock_quantity' => $inventory ? $inventory->stock_quantity : 0,
                            'min_stock' => $inventory ? $inventory->min_stock : 0,
                        ];
                    }
                }

                // Handle backlog service date calculation
                $serviceDate = null;
                $hmService = $backlog->plan_hm;

                if ($backlog->plan_pull_date) {
                    // Use existing plan_pull_date
                    $serviceDate = $backlog->plan_pull_date;
                } else if ($backlog->hm_found) {
                    // Calculate service date based on hm_found like daily report
                    $currentHm = $backlog->hm_found;
                    $serviceTargets = [250, 500, 1000, 2000, 4000];

                    foreach ($serviceTargets as $targetHm) {
                        if ($currentHm < $targetHm) {
                            $nextHmService = $targetHm - $currentHm;
                            $remainingDays = $nextHmService / 20;
                            $calculatedDate = Carbon::now()->addDays(ceil($remainingDays));

                            // Check if calculated date falls within selected range
                            if ($calculatedDate->between(Carbon::parse($startDate), Carbon::parse($endDate))) {
                                $serviceDate = $calculatedDate->format('Y-m-d');
                                $hmService = $targetHm;
                            }
                            break;
                        }
                    }
                }

                // Only add to results if service date is within range or calculated
                if ($serviceDate && Carbon::parse($serviceDate)->between(Carbon::parse($startDate), Carbon::parse($endDate))) {
                    $remainderTasks[] = [
                        'unit_id' => $backlog->unit->id,
                        'unit_name' => $backlog->unit->unit_code,
                        'unit_code' => $backlog->unit->unit_code,
                        'service_date' => $serviceDate,
                        'hm_service' => $hmService,
                        'source' => 'Backlog',
                        'source_id' => $backlog->id,
                        'parts' => $parts,
                    ];
                }
            }

            // 2. Get predicted service from daily reports
            $units = Unit::where('site_id', $siteId)->get();
            $serviceTargets = [250, 500, 1000, 2000, 4000];

            foreach ($units as $unit) {
                // Get latest daily report for this unit
                $latestReport = DailyReport::where('unit_id', $unit->id)
                    ->orderBy('date_in', 'desc')
                    ->first();

                if ($latestReport && $latestReport->hm) {
                    $currentHm = $latestReport->hm;

                    foreach ($serviceTargets as $targetHm) {
                        if ($currentHm < $targetHm) {
                            $nextHmService = $targetHm - $currentHm;
                            $remainingDays = $nextHmService / 20; // Assuming 20 HM per day
                            $serviceDate = Carbon::parse($latestReport->date_in)->addDays(ceil($remainingDays));

                            // Check if predicted service date falls within selected date range
                            if ($serviceDate->between(Carbon::parse($startDate), Carbon::parse($endDate))) {
                                // Get parts for milestone services (2000 & 4000) - optional
                                $parts = [];
                                if ($targetHm == 2000 || $targetHm == 4000) {
                                    $serviceParts = [
                                        ['part_code' => 'RD-A1180-PWB', 'part_name' => 'Receiver Dryer HD 78'],
                                        ['part_code' => 'EV-564587-PWB', 'part_name' => 'Replace Expantion Valve'],
                                        ['part_code' => 'OC-SP10-250C-PWB', 'part_name' => 'Oil Compressor SP10'],
                                    ];

                                    foreach ($serviceParts as $servicePart) {
                                        $inventory = PartInventory::where('part_code', $servicePart['part_code'])
                                            ->where('site_id', $siteId)
                                            ->first();

                                        $parts[] = [
                                            'part_code' => $servicePart['part_code'],
                                            'part_name' => $servicePart['part_name'],
                                            'quantity' => 1,
                                            'stock_quantity' => $inventory ? $inventory->stock_quantity : 0,
                                            'min_stock' => $inventory ? $inventory->min_stock : 0,
                                        ];
                                    }
                                }

                                $remainderTasks[] = [
                                    'unit_id' => $unit->id,
                                    'unit_name' => $unit->unit_code,
                                    'unit_code' => $unit->unit_code,
                                    'service_date' => $serviceDate->format('Y-m-d'),
                                    'hm_service' => $targetHm,
                                    'source' => 'Daily Report',
                                    'source_id' => $latestReport->daily_report_id,
                                    'parts' => $parts,
                                ];
                            }
                            break; // Only get the next service target
                        }
                    }
                }
            }

            // Sort by service date
            usort($remainderTasks, function($a, $b) {
                return strtotime($a['service_date']) - strtotime($b['service_date']);
            });

            return response()->json([
                'success' => true,
                'data' => $remainderTasks
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error loading remainder tasks: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get remainder task detail
     */
    public function getRemainderTaskDetail(Request $request)
    {
        $request->validate([
            'unit_id' => 'required|integer',
            'source' => 'required|string|in:Backlog,Daily Report',
            'source_id' => 'required|integer',
        ]);

        $unitId = $request->input('unit_id');
        $source = $request->input('source');
        $sourceId = $request->input('source_id');
        $siteId = session('site_id');

        if (!$siteId) {
            return response()->json([
                'success' => false,
                'message' => 'Site ID not found in session'
            ], 400);
        }

        try {
            $unit = Unit::where('id', $unitId)
                ->where('site_id', $siteId)
                ->first();

            if (!$unit) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unit not found'
                ], 404);
            }

            $detailData = [
                'unit_id' => $unit->id,
                'unit_name' => $unit->unit_code,
                'unit_code' => $unit->unit_code,
                'source' => $source,
                'parts' => [],
            ];

            if ($source === 'Backlog') {
                $backlog = Backlog::with(['backlogParts.part'])
                    ->where('id', $sourceId)
                    ->where('unit_code', $unit->unit_code)
                    ->first();

                if (!$backlog) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Backlog not found'
                    ], 404);
                }

                $detailData['service_date'] = $backlog->plan_pull_date;
                $detailData['hm_service'] = $backlog->plan_hm;
                $detailData['status'] = $backlog->status;
                $detailData['backlog_detail'] = [
                    'problem_description' => $backlog->problem_description,
                    'backlog_job' => $backlog->backlog_job,
                    'hm_found' => $backlog->hm_found,
                    'plan_hm' => $backlog->plan_hm,
                    'plan_pull_date' => $backlog->plan_pull_date,
                    'notes' => $backlog->notes,
                ];

                // Get parts
                foreach ($backlog->backlogParts as $backlogPart) {
                    if ($backlogPart->part) {
                        $inventory = PartInventory::where('part_code', $backlogPart->part_code)
                            ->where('site_id', $siteId)
                            ->first();

                        $detailData['parts'][] = [
                            'part_code' => $backlogPart->part_code,
                            'part_name' => $backlogPart->part->part_name,
                            'quantity' => $backlogPart->quantity,
                            'stock_quantity' => $inventory ? $inventory->stock_quantity : 0,
                            'min_stock' => $inventory ? $inventory->min_stock : 0,
                        ];
                    }
                }

            } else if ($source === 'Daily Report') {
                $dailyReport = DailyReport::with(['jobs', 'technicians'])
                    ->where('daily_report_id', $sourceId)
                    ->where('unit_id', $unitId)
                    ->first();

                if (!$dailyReport) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Daily report not found'
                    ], 404);
                }

                // Calculate predicted service date and HM
                $currentHm = $dailyReport->hm;
                $serviceTargets = [250, 500, 1000, 2000, 4000];
                $nextTarget = null;

                foreach ($serviceTargets as $target) {
                    if ($currentHm < $target) {
                        $nextTarget = $target;
                        break;
                    }
                }

                if ($nextTarget) {
                    $nextHmService = $nextTarget - $currentHm;
                    $remainingDays = $nextHmService / 20;
                    $serviceDate = Carbon::parse($dailyReport->date_in)->addDays(ceil($remainingDays));

                    $detailData['service_date'] = $serviceDate->format('Y-m-d');
                    $detailData['hm_service'] = $nextTarget;
                }

                $detailData['status'] = 'Prediksi Service';
                $detailData['daily_report_detail'] = [
                    'date_in' => $dailyReport->date_in,
                    'hm' => $dailyReport->hm,
                    'shift' => $dailyReport->shift,
                    'hour_in' => $dailyReport->hour_in,
                    'hour_out' => $dailyReport->hour_out,
                    'problem' => $dailyReport->problem,
                    'problem_component' => $dailyReport->problem_component,
                    'problem_description' => $dailyReport->problem_description,
                    'plan_fix' => $dailyReport->plan_fix,
                    'plan_rekomen' => $dailyReport->plan_rekomen,
                    'lifetime_component' => $dailyReport->lifetime_component,
                    'jobs' => $dailyReport->jobs->map(function($job) {
                        return [
                            'id' => $job->id,
                            'job_description' => $job->job_description ?? 'N/A'
                        ];
                    }),
                    'technicians' => $dailyReport->technicians->map(function($technician) {
                        return [
                            'id' => $technician->id,
                            'name' => $technician->name ?? 'N/A',
                            'employee_id' => $technician->employee_id ?? 'N/A'
                        ];
                    })
                ];

                // Get parts for milestone services (2000 & 4000)
                if ($nextTarget == 2000 || $nextTarget == 4000) {
                    $serviceParts = [
                        ['part_code' => 'RD-A1180-PWB', 'part_name' => 'Receiver Dryer HD 78'],
                        ['part_code' => 'EV-564587-PWB', 'part_name' => 'Replace Expantion Valve'],
                        ['part_code' => 'OC-SP10-250C-PWB', 'part_name' => 'Oil Compressor SP10'],
                    ];

                    foreach ($serviceParts as $servicePart) {
                        $inventory = PartInventory::where('part_code', $servicePart['part_code'])
                            ->where('site_id', $siteId)
                            ->first();

                        $detailData['parts'][] = [
                            'part_code' => $servicePart['part_code'],
                            'part_name' => $servicePart['part_name'],
                            'quantity' => 1,
                            'stock_quantity' => $inventory ? $inventory->stock_quantity : 0,
                            'min_stock' => $inventory ? $inventory->min_stock : 0,
                        ];
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => $detailData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error loading remainder task detail: ' . $e->getMessage()
            ], 500);
        }
    }
}
