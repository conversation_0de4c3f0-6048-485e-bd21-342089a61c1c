[2025-07-04 16:40:25] local.ERROR: Call to undefined relationship [part_problems] on model [App\Models\DailyReport]. {"userId":"2025005","exception":"[object] (Illuminate\\Database\\Eloquent\\RelationNotFoundException(code: 0): Call to undefined relationship [part_problems] on model [App\\Models\\DailyReport]. at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\RelationNotFoundException.php:35)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(878): Illuminate\\Database\\Eloquent\\RelationNotFoundException::make(Object(App\\Models\\DailyReport), 'part_problems')
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\Relation.php(120): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}()
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(874): Illuminate\\Database\\Eloquent\\Relations\\Relation::noConstraints(Object(Closure))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(848): Illuminate\\Database\\Eloquent\\Builder->getRelation('part_problems')
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(828): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelation(Array, 'part_problems', Object(Closure))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(794): Illuminate\\Database\\Eloquent\\Builder->eagerLoadRelations(Array)
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#7 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\Sites\\DashboardUnitSiteController.php(338): Illuminate\\Database\\Eloquent\\Builder->first()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\Sites\\DashboardUnitSiteController->getDailyReportDetail(Object(Illuminate\\Http\\Request))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Sites\\DashboardUnitSiteController), 'getDailyReportD...')
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminSite.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminSite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#56 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#57 {main}
"} 
[2025-07-05 08:42:22] local.ERROR: Attempt to read property "part_name" on null {"userId":"2025005","exception":"[object] (ErrorException(code: 0): Attempt to read property \"part_name\" on null at C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\DailyReportController.php:1044)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 1044)
#1 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\DailyReportController.php(1044): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 1044)
#2 [internal function]: App\\Http\\Controllers\\DailyReportController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\PartInventory), 7)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php(388): Illuminate\\Support\\Collection->map(Object(Closure))
#6 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\DailyReportController.php(1041): Illuminate\\Database\\Eloquent\\Collection->map(Object(Closure))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\DailyReportController->searchParts(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DailyReportController), 'searchParts')
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminSite.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminSite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}
"} 
[2025-07-05 11:00:00] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-05 11:00:00] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-05 11:00:00] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-05 11:02:51] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-07-05 14:17:50] local.INFO: Unit Schedule getData - Session data: {"role":"adminsite","site_id":"PPA","request_site_id":null} 
[2025-07-05 14:17:50] local.INFO: Filtering by site for non-HO user: {"site_id":"PPA"} 
[2025-07-05 14:17:50] local.INFO: Units retrieved: {"count":108,"units":{"20":"HD7835","21":"HD78128","22":"HD78347","23":"H57105AMM","24":"HD7897","25":"HD78136","26":"HD78160","27":"HD78435","28":"H57154AMM","29":"HD78116","35":"HD78145","36":"HD78161","37":"HD78232","38":"H57089AMM","39":"HD78453","40":"H57159AMM","41":"H57018AMM","42":"HD78234","48":"C577026AMM","49":"HD78263","50":"H57146AMM","51":"HD78235","52":"HD78159","53":"HD78458","56":"H57008AMM","57":"HD7827","58":"H57024AMM","61":"HD78170","62":"HD78112","63":"H57015AMM","64":"HD78262","65":"H57023AMM","72":"HD78102","73":"HD78348","74":"H57145AMM","75":"H57094AMM","76":"H57025AMM","77":"HD7874","78":"H57084AMM","79":"HD7823","80":"HD78125","82":"HD78142","83":"HD78131","84":"HD78152","89":"HD78165","90":"HD7826","91":"HD78233","93":"HD78168","97":"H57088AMM","98":"HD78144","99":"H57021AMM","100":"HD78105","101":"HD78143","102":"HD78231","108":"HD78135","109":"HD78459","110":"H57148AMM","111":"HD78264","112":"H57117AMM","121":"HD7879","122":"H57155AMM","123":"H57019AMM","124":"HD78127","125":"HD7805","126":"H57129AMM","127":"HD78440","128":"H57029AMM","129":"HD78419","130":"HD7892","131":"HD78423","132":"H57026AMM","133":"HD78451","134":"HD78106","135":"HD7878","136":"H57134AMM","137":"H57017AMM","138":"HD78460","139":"H57004AMM","140":"HD7884","141":"H57075AMM","142":"HD78420","143":"H57153AMM","144":"HD78428","145":"HD78206","146":"H57030AMM","173":"HD78104","174":"HD78236","175":"HD78265","176":"H57001AMM","182":"H57137AMM","183":"H57125AMM","184":"H57009AMM","185":"H57007AMM","189":"HD78132","190":"HD78257","193":"HD78167","194":"HD78157","198":"HD78241","199":"HD78261","202":"HD78158","203":"HD78240","205":"HD78163","206":"HD78269","207":"HD78276","215":"H57013AMM","222":"HD78424","231":"H57126AMM","232":"HD78239"}} 
[2025-07-07 08:11:56] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `users` where `username` = kusumadwiadminsite limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `users` where `username` = kusumadwiadminsite limit 1) at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(139): Illuminate\\Database\\Eloquent\\Builder->first()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(396): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Object(SensitiveParameterValue))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(339): Illuminate\\Auth\\SessionGuard->attempt(Array)
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Auth\\AuthManager->__call('attempt', Array)
#15 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\AuthController.php(45): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#16 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\AuthController.php(35): App\\Http\\Controllers\\AuthController->attemptRegularLogin(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\AuthController->login(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AuthController), 'login')
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#64 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(139): Illuminate\\Database\\Eloquent\\Builder->first()
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(396): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Object(SensitiveParameterValue))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(339): Illuminate\\Auth\\SessionGuard->attempt(Array)
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Auth\\AuthManager->__call('attempt', Array)
#26 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\AuthController.php(45): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#27 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\AuthController.php(35): App\\Http\\Controllers\\AuthController->attemptRegularLogin(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\AuthController->login(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AuthController), 'login')
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#74 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#75 {main}
"} 
[2025-07-07 08:15:52] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `users` where `username` = kusumadwiadminsite limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select * from `users` where `username` = kusumadwiadminsite limit 1) at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(139): Illuminate\\Database\\Eloquent\\Builder->first()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(396): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Object(SensitiveParameterValue))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(339): Illuminate\\Auth\\SessionGuard->attempt(Array)
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Auth\\AuthManager->__call('attempt', Array)
#15 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\AuthController.php(45): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#16 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\AuthController.php(35): App\\Http\\Controllers\\AuthController->attemptRegularLogin(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\AuthController->login(Object(Illuminate\\Http\\Request))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AuthController), 'login')
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#60 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#61 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#63 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#64 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(185): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1231): call_user_func(Object(Closure))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1267): Illuminate\\Database\\Connection->getPdo()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(512): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(407): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(979): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(958): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(781): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select * from `...', Array, Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(398): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3106): Illuminate\\Database\\Connection->select('select * from `...', Array, true)
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3091): Illuminate\\Database\\Query\\Builder->runSelect()
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3676): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3090): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(811): Illuminate\\Database\\Query\\Builder->get(Array)
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(793): Illuminate\\Database\\Eloquent\\Builder->getModels(Array)
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Concerns\\BuildsQueries.php(343): Illuminate\\Database\\Eloquent\\Builder->get(Array)
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php(139): Illuminate\\Database\\Eloquent\\Builder->first()
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php(396): Illuminate\\Auth\\EloquentUserProvider->retrieveByCredentials(Object(SensitiveParameterValue))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php(339): Illuminate\\Auth\\SessionGuard->attempt(Array)
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Auth\\AuthManager->__call('attempt', Array)
#26 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\AuthController.php(45): Illuminate\\Support\\Facades\\Facade::__callStatic('attempt', Array)
#27 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\AuthController.php(35): App\\Http\\Controllers\\AuthController->attemptRegularLogin(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\AuthController->login(Object(Illuminate\\Http\\Request))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\AuthController), 'login')
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#71 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#72 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#73 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#74 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#75 {main}
"} 
[2025-07-07 10:15:02] local.INFO: Part update details: {"part_name":"HOSE DISCHARGE 1/2 NICHIRIN","part_id":"100126","part_code":"HS-N12-PWB","is_new_part":false,"old_quantity":1.0,"new_quantity":"1","quantity_difference":0.0,"current_stock":2619.0} 
[2025-07-07 10:15:02] local.INFO: Part update details: {"part_name":"FITTING NICIRIN 1/2B R134","part_id":"100113","part_code":"FT-12B-N-R134-PWB","is_new_part":false,"old_quantity":1.0,"new_quantity":"1","quantity_difference":0.0,"current_stock":44.0} 
[2025-07-07 10:17:43] local.INFO: Part update details: {"part_name":"FREON R11","part_id":"100128","part_code":"FRN-R11-PWB","is_new_part":false,"old_quantity":1.0,"new_quantity":"1","quantity_difference":0.0,"current_stock":2.0} 
[2025-07-07 10:17:43] local.INFO: Part update details: {"part_name":"FREON KLEA","part_id":"100127","part_code":"FRN-KLEA-PWB","is_new_part":false,"old_quantity":1.0,"new_quantity":"1","quantity_difference":0.0,"current_stock":3.0} 
[2025-07-07 13:11:42] local.INFO: Updated DO number for Unit ID: 180, Unit Code: EX1347, New DO Number: jknoil  
[2025-07-07 13:11:42] local.INFO: Updated DO number for Unit ID: 195, Unit Code: GR3050, New DO Number: jknoil  
[2025-07-07 13:11:42] local.INFO: Updated DO number for Unit ID: 187, Unit Code: GR3056, New DO Number: jknoil  
[2025-07-07 14:15:17] local.INFO: User pwb20212025 logged in successfully using token. employee_id: 666, site_id:   
[2025-07-07 14:15:18] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-07 14:15:18] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-07 14:15:18] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-07 14:15:18] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-07 14:15:18] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-07 14:15:18] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-07 14:15:18] local.INFO: Sites Data Response {"count":5} 
[2025-07-07 14:15:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:15:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:15:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:21:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:21:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:21:37] local.INFO: Updated DO number for Unit ID: 180, Unit Code: EX1347, New DO Number: jknoil  
[2025-07-07 14:21:37] local.INFO: Updated DO number for Unit ID: 195, Unit Code: GR3050, New DO Number: jknoil  
[2025-07-07 14:21:37] local.INFO: Updated DO number for Unit ID: 187, Unit Code: GR3056, New DO Number: jknoil  
[2025-07-07 14:34:10] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:34:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:35:02] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:35:02] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:35:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:35:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:35:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:35:54] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:35:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:35:56] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:02] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:02] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:09] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:23] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:33] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:39] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:39] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:36:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:36:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:37:05] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:37:05] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:38:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:38:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:38:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:38:36] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:38:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:38:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:38:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:38:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:38:48] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:38:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:39:02] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:39:02] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:39:24] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:39:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:39:26] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:39:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:39:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:39:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:39:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:39:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:39:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:39:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:39:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:39:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:40:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:40:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:40:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:40:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:40:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:40:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:40:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:40:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:40:48] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:40:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:40:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:40:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:40:53] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:40:53] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:40:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:40:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:42:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:42:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:43:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:43:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:43:06] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:43:06] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:48:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:48:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:49:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:49:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:50:43] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:50:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:50:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:50:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:50:45] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:50:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:51:38] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:51:39] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:52:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:52:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 14:57:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 14:57:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:01:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:01:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:01:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:01:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:01:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:01:38] local.INFO: Best Parts Data Request {"start_date":"2025-06-29","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-06-29 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:01:39] local.INFO: Best Parts Data Request {"start_date":"2025-06-29","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-06-29 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:01:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:01:43] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:01:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:01:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:01:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:01:47] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:01:48] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:01:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:01:52] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:01:52] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:02:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-10","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-10 23:59:59"} 
[2025-07-07 15:02:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-10","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-10 23:59:59"} 
[2025-07-07 15:02:09] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-10","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-10 23:59:59"} 
[2025-07-07 15:17:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:17:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:17:36] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:17:36] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:17:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:17:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:17:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:17:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:18:24] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:18:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:18:26] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:18:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:18:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:18:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:18:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:18:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:18:47] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:18:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:18:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:18:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:18:52] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:18:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:18:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:18:54] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:19:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:19:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:19:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:19:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:19:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:19:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:19:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:19:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:20:36] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:20:36] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:20:39] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:20:39] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:21:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:21:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:21:11] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:21:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:21:14] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:21:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:21:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:21:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:21:21] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:21:21] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:21:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:21:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:21:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:21:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:21:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:21:56] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:21:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:21:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:22:10] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:22:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:22:14] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:22:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:22:27] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:22:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:22:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:22:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:22:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:22:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:22:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:23:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:23:05] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:23:06] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:23:21] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:23:21] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:23:26] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:23:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:23:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:23:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:23:34] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:23:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:23:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:23:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:23:39] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:23:39] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:23:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:23:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:23:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:23:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:24:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:24:04] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:24:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:24:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:25:12] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:25:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:25:13] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:25:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:25:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:25:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:25:23] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:25:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:26:12] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:26:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:26:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:26:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:26:23] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:26:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:26:38] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:26:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:26:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:26:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:31:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:31:47] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:31:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:31:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:31:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:31:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:32:10] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:32:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:32:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:32:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:34:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:34:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:34:33] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:34:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:34:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:34:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:34:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:34:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:38:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:38:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:38:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:38:46] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:38:48] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:38:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:38:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:38:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:38:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:39:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:39:03] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:39:03] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:39:12] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:39:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:39:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:39:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:39:21] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:39:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:39:26] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:39:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:39:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:39:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:39:48] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:39:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:39:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:39:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:39:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:39:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:40:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:40:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:40:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:40:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:40:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:40:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:40:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:40:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:41:14] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:41:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:41:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:41:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:41:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:41:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:41:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:41:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:42:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:42:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:43:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:43:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:43:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:43:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:43:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:43:38] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:43:39] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:44:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:44:09] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:44:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:44:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:44:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:44:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:44:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:44:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:44:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:44:39] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:44:39] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:44:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:44:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:45:01] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:45:02] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:45:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:45:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 15:46:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 15:46:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:03:11] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:03:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:08:55] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:08:55] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:22:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:22:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:23:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:23:01] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:23:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:23:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:23:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:23:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:23:55] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:23:55] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:23:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:23:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:24:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:24:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:24:11] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:24:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:24:13] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:24:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:24:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:24:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:24:23] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:24:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:26:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:26:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:26:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:26:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:27:01] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:27:01] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:27:11] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:27:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:27:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:27:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:27:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:27:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:27:36] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:27:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:31:52] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:31:53] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:32:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:32:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:32:24] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:32:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:32:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:32:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:32:27] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:32:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:33:23] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:33:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:33:32] local.INFO: Unit data: {"id":24,"site_id":"PPA","unit_code":"HD7897","unit_type":"KOMATSU 785-7R","nopr":null,"noqtn":null,"do_number":"DO PPA-BIB 375","noSPB":"PWB-PPA-BIB\/V\/2025\/375","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-16T11:02:11.000000Z","pekerjaan":null,"HMKM":null,"SHIFT":null,"LOKASI":null,"parts":[{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":true,"date_priority":"2025-06-19","min_stock":2,"max_stock":5,"oum":null,"stock_quantity":0,"created_at":null,"updated_at":"2025-07-02T07:15:27.000000Z","price":"1800000.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":2800000,"purchase_price":1300000,"eum":"EA","created_at":null,"updated_at":"2025-06-28T11:57:17.000000Z"}}}]}  
[2025-07-07 16:33:32] local.INFO: Parts data: [{"id":44,"unit_id":24,"part_inventory_id":2011,"quantity":1,"price":1800000,"eum":"TBG","created_at":"2025-05-08T16:32:57.000000Z","updated_at":"2025-05-08T16:32:57.000000Z","part_inventory":{"part_inventory_id":2011,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"PPA","priority":true,"date_priority":"2025-06-19","min_stock":2,"max_stock":5,"oum":null,"stock_quantity":0,"created_at":null,"updated_at":"2025-07-02T07:15:27.000000Z","price":"1800000.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":2800000,"purchase_price":1300000,"eum":"EA","created_at":null,"updated_at":"2025-06-28T11:57:17.000000Z"}}}]  
[2025-07-07 16:36:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:36:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:36:43] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:36:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:36:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:36:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:36:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:36:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:37:06] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:37:06] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:37:45] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:37:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:37:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:37:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:37:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:38:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:38:01] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:38:01] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:38:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:38:05] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:38:05] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:38:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:38:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:46:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:46:05] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:47:31] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-07 16:47:31] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-07 16:47:31] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-07 16:47:31] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-07 16:47:31] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-07 16:47:31] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-07 16:47:31] local.INFO: Sites Data Response {"count":5} 
[2025-07-07 16:47:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:47:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:47:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:52:43] local.INFO: Unit data: {"id":196,"site_id":"DH","unit_code":"FT1130","unit_type":"FUEL TRUCK MERCY 2528","nopr":null,"noqtn":null,"do_number":"BAPP DH-WKP 001","noSPB":null,"created_at":"2025-06-23T11:43:40.000000Z","updated_at":"2025-06-25T15:42:43.000000Z","pekerjaan":null,"HMKM":null,"SHIFT":null,"LOKASI":null,"parts":[{"id":446,"unit_id":196,"part_inventory_id":100127,"quantity":1,"price":2800000,"eum":"TBG","created_at":"2025-06-23T11:43:40.000000Z","updated_at":"2025-06-23T11:43:40.000000Z","part_inventory":{"part_inventory_id":100127,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"DH","priority":true,"date_priority":"2025-06-19","min_stock":2,"max_stock":5,"oum":"TBG","stock_quantity":3,"created_at":"2025-05-27T08:42:20.000000Z","updated_at":"2025-07-01T11:40:47.000000Z","price":"2800000.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":2800000,"purchase_price":1300000,"eum":"EA","created_at":null,"updated_at":"2025-06-28T11:57:17.000000Z"}}}]}  
[2025-07-07 16:52:43] local.INFO: Parts data: [{"id":446,"unit_id":196,"part_inventory_id":100127,"quantity":1,"price":2800000,"eum":"TBG","created_at":"2025-06-23T11:43:40.000000Z","updated_at":"2025-06-23T11:43:40.000000Z","part_inventory":{"part_inventory_id":100127,"part_code":"FRN-KLEA-PWB","site_part_name":null,"site_id":"DH","priority":true,"date_priority":"2025-06-19","min_stock":2,"max_stock":5,"oum":"TBG","stock_quantity":3,"created_at":"2025-05-27T08:42:20.000000Z","updated_at":"2025-07-01T11:40:47.000000Z","price":"2800000.00","item_code":null,"part":{"part_code":"FRN-KLEA-PWB","part_name":"FREON KLEA","bin_location":"-","part_type":"AC","price":2800000,"purchase_price":1300000,"eum":"EA","created_at":null,"updated_at":"2025-06-28T11:57:17.000000Z"}}}]  
[2025-07-07 16:57:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:57:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:57:53] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:57:53] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:57:55] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:57:55] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:57:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:57:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:57:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:57:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:58:06] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:58:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:58:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:58:43] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:58:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:59:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:59:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 16:59:27] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 16:59:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:01:03] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:01:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:01:04] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:01:06] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:01:06] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:01:09] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:01:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:01:13] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:01:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:01:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:01:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:02:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:02:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:02:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:02:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:02:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:02:23] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:02:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:02:36] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:02:36] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:03:01] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:03:01] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:03:03] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:03:03] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:03:12] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:03:13] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:03:14] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:03:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:03:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:03:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-07","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-07 23:59:59"} 
[2025-07-07 17:03:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-07 17:04:26] local.ERROR: Error getting transaction count: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select count(*) as aggregate from `stock_transactions` where `destination_siteid` = DH and `status` not in (selesai, return))  
[2025-07-07 19:33:04] local.INFO: Unit data: {"id":180,"site_id":"DH","unit_code":"EX1347","unit_type":"EXCAVATOR SANY SY500H","nopr":null,"noqtn":null,"do_number":"jknoil","noSPB":null,"created_at":"2025-06-19T15:58:45.000000Z","updated_at":"2025-07-07T05:11:42.000000Z","pekerjaan":null,"HMKM":null,"SHIFT":null,"LOKASI":null,"parts":[{"id":399,"unit_id":180,"part_inventory_id":2058,"quantity":1,"price":465000,"eum":"EA","created_at":"2025-06-19T15:58:45.000000Z","updated_at":"2025-06-19T15:58:45.000000Z","part_inventory":{"part_inventory_id":2058,"part_code":"AF-003-I-PWB","site_part_name":null,"site_id":"DH","priority":false,"date_priority":"2025-06-25","min_stock":2,"max_stock":4,"oum":null,"stock_quantity":1,"created_at":"2025-05-14T09:03:56.000000Z","updated_at":"2025-07-01T11:39:58.000000Z","price":"465000.00","item_code":null,"part":{"part_code":"AF-003-I-PWB","part_name":"AIR FILTER PC300 (IN)","bin_location":"-","part_type":"AC","price":465000,"purchase_price":155000,"eum":"EA","created_at":null,"updated_at":"2025-06-26T17:00:33.000000Z"}}},{"id":400,"unit_id":180,"part_inventory_id":100131,"quantity":1,"price":550000,"eum":"EA","created_at":"2025-06-19T15:58:45.000000Z","updated_at":"2025-06-19T15:58:45.000000Z","part_inventory":{"part_inventory_id":100131,"part_code":"RD-TCP-R134-PJG-PWB","site_part_name":null,"site_id":"DH","priority":true,"date_priority":"2025-06-19","min_stock":1,"max_stock":3,"oum":null,"stock_quantity":1,"created_at":"2025-05-27T16:23:25.000000Z","updated_at":"2025-07-01T11:39:58.000000Z","price":"550000.00","item_code":null,"part":{"part_code":"RD-TCP-R134-PJG-PWB","part_name":"RECEIVER DRYER ND TANCAP R134A PJG KOMATSU","bin_location":"-","part_type":"AC","price":550000,"purchase_price":250000,"eum":"EA","created_at":null,"updated_at":"2025-06-28T14:18:01.000000Z"}}},{"id":401,"unit_id":180,"part_inventory_id":100129,"quantity":1,"price":556500,"eum":"LTR","created_at":"2025-06-19T15:58:45.000000Z","updated_at":"2025-06-19T15:58:45.000000Z","part_inventory":{"part_inventory_id":100129,"part_code":"OC-SP10-250C-PWB","site_part_name":null,"site_id":"DH","priority":true,"date_priority":"2025-06-19","min_stock":2,"max_stock":5,"oum":"LTR","stock_quantity":8,"created_at":"2025-05-27T08:42:44.000000Z","updated_at":"2025-07-01T11:39:58.000000Z","price":"556500.00","item_code":null,"part":{"part_code":"OC-SP10-250C-PWB","part_name":"OIL SANDEN SP10","bin_location":"-","part_type":"AC","price":556500,"purchase_price":185000,"eum":"EA","created_at":null,"updated_at":"2025-06-28T13:26:03.000000Z"}}},{"id":402,"unit_id":180,"part_inventory_id":100139,"quantity":1,"price":910000,"eum":"EA","created_at":"2025-06-19T15:58:45.000000Z","updated_at":"2025-06-19T15:58:45.000000Z","part_inventory":{"part_inventory_id":100139,"part_code":"EV-564587-PWB","site_part_name":null,"site_id":"DH","priority":true,"date_priority":"2025-06-19","min_stock":2,"max_stock":6,"oum":null,"stock_quantity":1,"created_at":"2025-05-27T16:45:35.000000Z","updated_at":"2025-07-01T11:39:58.000000Z","price":"910000.00","item_code":null,"part":{"part_code":"EV-564587-PWB","part_name":"EXPANTION VALVE HD 465 \/ 785 - 7R","bin_location":"-","part_type":"AC","price":910000,"purchase_price":300000,"eum":"EA","created_at":null,"updated_at":"2025-06-26T17:52:29.000000Z"}}}]}  
[2025-07-07 19:33:04] local.INFO: Parts data: [{"id":399,"unit_id":180,"part_inventory_id":2058,"quantity":1,"price":465000,"eum":"EA","created_at":"2025-06-19T15:58:45.000000Z","updated_at":"2025-06-19T15:58:45.000000Z","part_inventory":{"part_inventory_id":2058,"part_code":"AF-003-I-PWB","site_part_name":null,"site_id":"DH","priority":false,"date_priority":"2025-06-25","min_stock":2,"max_stock":4,"oum":null,"stock_quantity":1,"created_at":"2025-05-14T09:03:56.000000Z","updated_at":"2025-07-01T11:39:58.000000Z","price":"465000.00","item_code":null,"part":{"part_code":"AF-003-I-PWB","part_name":"AIR FILTER PC300 (IN)","bin_location":"-","part_type":"AC","price":465000,"purchase_price":155000,"eum":"EA","created_at":null,"updated_at":"2025-06-26T17:00:33.000000Z"}}},{"id":400,"unit_id":180,"part_inventory_id":100131,"quantity":1,"price":550000,"eum":"EA","created_at":"2025-06-19T15:58:45.000000Z","updated_at":"2025-06-19T15:58:45.000000Z","part_inventory":{"part_inventory_id":100131,"part_code":"RD-TCP-R134-PJG-PWB","site_part_name":null,"site_id":"DH","priority":true,"date_priority":"2025-06-19","min_stock":1,"max_stock":3,"oum":null,"stock_quantity":1,"created_at":"2025-05-27T16:23:25.000000Z","updated_at":"2025-07-01T11:39:58.000000Z","price":"550000.00","item_code":null,"part":{"part_code":"RD-TCP-R134-PJG-PWB","part_name":"RECEIVER DRYER ND TANCAP R134A PJG KOMATSU","bin_location":"-","part_type":"AC","price":550000,"purchase_price":250000,"eum":"EA","created_at":null,"updated_at":"2025-06-28T14:18:01.000000Z"}}},{"id":401,"unit_id":180,"part_inventory_id":100129,"quantity":1,"price":556500,"eum":"LTR","created_at":"2025-06-19T15:58:45.000000Z","updated_at":"2025-06-19T15:58:45.000000Z","part_inventory":{"part_inventory_id":100129,"part_code":"OC-SP10-250C-PWB","site_part_name":null,"site_id":"DH","priority":true,"date_priority":"2025-06-19","min_stock":2,"max_stock":5,"oum":"LTR","stock_quantity":8,"created_at":"2025-05-27T08:42:44.000000Z","updated_at":"2025-07-01T11:39:58.000000Z","price":"556500.00","item_code":null,"part":{"part_code":"OC-SP10-250C-PWB","part_name":"OIL SANDEN SP10","bin_location":"-","part_type":"AC","price":556500,"purchase_price":185000,"eum":"EA","created_at":null,"updated_at":"2025-06-28T13:26:03.000000Z"}}},{"id":402,"unit_id":180,"part_inventory_id":100139,"quantity":1,"price":910000,"eum":"EA","created_at":"2025-06-19T15:58:45.000000Z","updated_at":"2025-06-19T15:58:45.000000Z","part_inventory":{"part_inventory_id":100139,"part_code":"EV-564587-PWB","site_part_name":null,"site_id":"DH","priority":true,"date_priority":"2025-06-19","min_stock":2,"max_stock":6,"oum":null,"stock_quantity":1,"created_at":"2025-05-27T16:45:35.000000Z","updated_at":"2025-07-01T11:39:58.000000Z","price":"910000.00","item_code":null,"part":{"part_code":"EV-564587-PWB","part_name":"EXPANTION VALVE HD 465 \/ 785 - 7R","bin_location":"-","part_type":"AC","price":910000,"purchase_price":300000,"eum":"EA","created_at":null,"updated_at":"2025-06-26T17:52:29.000000Z"}}}]  
[2025-07-07 19:53:07] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoice_unit_transactions' already exists (Connection: mysql, SQL: create table `invoice_unit_transactions` (`id` bigint unsigned not null auto_increment primary key, `invoice_id` bigint unsigned not null, `unit_transaction_id` bigint unsigned not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoice_unit_transactions' already exists (Connection: mysql, SQL: create table `invoice_unit_transactions` (`id` bigint unsigned not null auto_increment primary key, `invoice_id` bigint unsigned not null, `unit_transaction_id` bigint unsigned not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `i...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `i...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `i...')
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('invoice_unit_tr...', Object(Closure))
#6 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_04_23_021926_create_invoice_unit_transactions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_04_23_0219...', Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_04_23_0219...', Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 50, false)
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoice_unit_transactions' already exists at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:571)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(571): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `i...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `i...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `i...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `i...')
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('invoice_unit_tr...', Object(Closure))
#8 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_04_23_021926_create_invoice_unit_transactions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_04_23_0219...', Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_04_23_0219...', Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 50, false)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
