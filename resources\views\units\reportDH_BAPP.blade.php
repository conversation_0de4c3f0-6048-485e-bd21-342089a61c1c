<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Berita Acara Pemakaian Part</title>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 11px;
            line-height: 1.4;
            margin: 20px;
        }

        /* Header styles */
        .header-container {
            width: 100%;
            margin-bottom: 15px;
        }

        .logo-left {
            float: left;
            width: 20%;
        }

        .title-center {
            float: left;
            width: 60%;
            text-align: center;
        }

        .number-right {
            float: right;
            width: 20%;
            text-align: right;
        }

        .header-title {
            background-color:rgb(29, 87, 138);
            color: white;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
        }

        /* Customer info table */
        .customer-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .customer-table td {
            border: 1px solid #000;
            padding: 1px;
            padding-left: 4px;
        }

        .customer-table .label {
            width: 10%;
            font-weight: normal;
        }

        /* Main table styles */
        .main-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .main-table th,
        .main-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: center;
            font-size: 10px;
        }

        .main-table th {
            background-color:rgb(29, 87, 138);
            color: white;
            font-weight: bold;
        }

        /* Total section */
        .total-section {
            width: 30%;
            float: right;
            margin-bottom: 20px;
        }

        .total-table {
            width: 100%;
            border-collapse: collapse;
        }

        .total-table td {
            border: 1px solid #000;
            padding: 1px;
            text-align: left;
        }

        .total-table .total-label {
            font-weight: bold;
        }

        /* Signature section */
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }

        .signature-table td {
            width: 33.33%;
            text-align: center;
            vertical-align: bottom;
            font-weight: bold;
            font-size: 10px;
        }

        .signature-stamp {
            width: 100px;
            height: 100px;
            margin: 0 auto;
            position: relative;
        }

        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }

        /* Adjust for landscape orientation */
        @page {
            size: landscape;
        }
    </style>
</head>

<body>
    <!-- Header with logo, title and number -->
    <div class="header-container clearfix">
        <div class="logo-left" style="text-align: center;">
            <img src="{{ public_path('assets/images/logo-small.png') }}" alt="PWB LOGO" style="width: 80px;">
            <p><strong>PT PUTERA WIBOWO BORNEO</strong></p>
        </div>
        <div class="title-center">
            <div class="header-title">BERITA ACARA PEMAKAIAN PART</div>
            <p>PT. DARMA HENWA SITE KINTAP</p>
            <p>Tanggal {{ $transactions[0]->tanggalstart ? \Carbon\Carbon::parse($transactions[0]->tanggalstart)->translatedFormat('j F Y') : '-' }} s/d {{ $transactions[0]->tanggalend ? \Carbon\Carbon::parse($transactions[0]->tanggalend)->translatedFormat('j F Y') : '-' }}</p>
            <p style="font-weight: bolder;">NO BA : {{ $transactions[0]->noBA ?? ' - ' }}</p>
        </div>
    </div>

    <!-- Main Content Table -->
    <table class="main-table">
        <thead>
            <tr>
                <th style="width:5%;">NO</th>
                <th style="width:10%;">DATE</th>
                <th style="width:15%;">IREQ</th>
                <th style="width:10%;">UNIT</th>
                <th style="width:15%;">PART NUMBER</th>
                <th style="width:25%;">PART NAME</th>
                <th style="width:5%;">QTY</th>
                <th style="width:5%;">UOM</th>
                <th style="width:14%;">UNIT PRICE</th>
                <th style="width:14%;">AMOUNT</th>
            </tr>
        </thead>
        <tbody>
            @if(count($transactions) > 0)
            @php
            $no = 1;
            $totalAmount = 0;
            // Sort transactions by MR date
            $sortedTransactions = $transactions->sortBy(function($transaction) {
                return $transaction->mr_date ?? '9999-12-31'; // Default to far future date if null
            });
            @endphp
            @foreach($sortedTransactions as $transaction)
            @php
            // Use MR date for display, consistent with the sorting
            $transactionDate = \Carbon\Carbon::parse($transaction->mr_date ?? now())->translatedFormat('d F Y');
            $sortedParts = $transaction->parts->sortBy('id');
            @endphp
            @foreach($sortedParts as $part)
            @php
            $subtotal = $part->price * $part->quantity;
            $totalAmount += $subtotal;
            @endphp
            <tr>
                <td>{{ $no++ }}</td>
                <td>{{ $transactionDate }}</td>
                <td>{{ $transaction->noireq ?? '-' }}</td>
                <td>{{ $transaction->unit->unit_code }}</td>
                <td>{{ $part->partInventory->part->part_code }}</td>
                <td>{{ $part->partInventory->part->part_name }}</td>
                <td>{{ $part->quantity}}</td>
                <td>{{ $part->partInventory->oum ?? $part->eum }}</td>
                <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span> {{ number_format($part->price, 0, ',', '.') }}</td>
                <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span> {{ number_format($subtotal, 0, ',', '.') }}</td>
            </tr>
            @endforeach
            @endforeach
            @else
            @for($i = 1; $i <= 10; $i++)
                <tr>
                <td>{{ $i }}</td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
                </tr>
                @endfor
                @endif
                <tr>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="text-align: left;">TOTAL</td>
                    <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span> {{ number_format($totalAmount ?? 0, 0, ',', '.') }}</td>
                </tr>
                <tr style="background-color: #ffff">
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="text-align: left;">PPN 11%</td>
                    <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span> {{ number_format(($totalAmount ?? 0) * 0.11, 0, ',', '.') }}</td>
                </tr>
                <tr>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="text-align: left;">GRAND TOTAL</td>
                    <td style="text-align: right;"><span style="float: left; padding-left: 4px">Rp</span> {{ number_format(($totalAmount ?? 0) * 1.11, 0, ',', '.') }}</td>
                </tr>
        </tbody>
    </table>

    <!-- Signature Section -->
    <div class="clearfix">
        <table class="signature-table">
            <tr>
                <td>
                    <div>Approve,</div>
                </td>
                <td>
                    <div>Approve,</div>
                </td>
                <td>
                    <div>Approve,</div>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 60px;">
                    <p style="padding: 0;margin: 0;">{{session('name')}}</p>
                    <div>(ADMIN PWB)</div>
                </td>
                <td style="padding-top: 60px;">
                    <p style="padding: 0;margin: 0;">Agung Nugroho</p>
                    <div>(Warehouse Supervisor)</div>
                </td>
                <td style="padding-top: 60px;">
                    <p style="padding: 0;margin: 0;">Chocky Hasian Simanjuntak</p>
                    <div>(Warehouse Manager)</div>
                </td>
            </tr>
        </table>
    </div>
</body>

</html>