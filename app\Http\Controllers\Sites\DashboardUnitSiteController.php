<?php

namespace App\Http\Controllers\Sites;

use App\Http\Controllers\Controller;
use App\Models\Unit;
use App\Models\DailyReport;
use App\Models\Backlog;
use App\Models\Job;
use App\Models\SiteOutStock;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardUnitSiteController extends Controller
{
    /**
     * Display the dashboard unit site page
     */
    public function index()
    {
        return view('sites.dashboard-unit-site');
    }

    /**
     * Search units for AJAX autocomplete
     */
    public function searchUnits(Request $request)
    {
        try {
            $query = $request->get('query', '');
            $siteId = session('site_id');

            if (empty($query)) {
                return response()->json([
                    'success' => true,
                    'data' => []
                ]);
            }

            $units = Unit::where('site_id', $siteId)
                ->where(function ($q) use ($query) {
                    $q->where('unit_code', 'LIKE', "%{$query}%")
                        ->orWhere('unit_type', 'LIKE', "%{$query}%");
                })
                ->whereHas('dailyReports') // Only units with daily reports
                ->select('id', 'unit_code', 'unit_type', 'site_id')
                ->orderBy('unit_code')
                ->limit(15)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $units,
                'count' => $units->count()
            ]);

        } catch (\Exception $e) {
            \Log::error('Unit search error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mencari unit',
                'data' => []
            ], 500);
        }
    }

    /**
     * Get dashboard data for selected unit
     */
    public function getDashboardData(Request $request)
    {
        $unitId = $request->get('unit_id');

        if (!$unitId) {
            return response()->json([
                'success' => false,
                'message' => 'Unit ID is required'
            ], 400);
        }

        $unit = Unit::with(['site'])->find($unitId);

        if (!$unit) {
            return response()->json([
                'success' => false,
                'message' => 'Unit not found'
            ], 404);
        }

        // Check if unit belongs to current site
        $siteId = session('site_id');
        if ($unit->site_id !== $siteId) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to unit'
            ], 403);
        }

        $data = [
            'unit' => $unit,
            'daily_reports' => $this->getDailyReports($unitId),
            'latest_report' => $this->getLatestReport($unitId),
            'open_backlogs' => $this->getOpenBacklogs($unit->unit_code),
            'closed_backlogs' => $this->getClosedBacklogs($unit->unit_code),
            'part_usage_stats' => $this->getPartUsageStats($unitId),
            'monthly_usage_stats' => $this->getMonthlyUsageStats($unitId),
            'service_component_stats' => $this->getServiceComponentStats($unitId)
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Get daily reports for unit
     */
    private function getDailyReports($unitId)
    {
        return DailyReport::where('unit_id', $unitId)
            ->with(['jobs'])
            ->orderBy('date_in', 'desc')
            ->limit(20)
            ->get()
            ->map(function ($report) {
                return [
                    'daily_report_id' => $report->daily_report_id,
                    'date_in' => $report->date_in->format('d/m/Y'),
                    'hm' => $report->hm,
                    'problem' => $report->problem,
                    'problem_description' => $report->problem_description,
                    'shift' => $report->shift,
                    'jobs' => $report->jobs->pluck('job_description')->toArray()
                ];
            });
    }

    /**
     * Get latest daily report with technicians
     */
    private function getLatestReport($unitId)
    {
        $report = DailyReport::where('unit_id', $unitId)
            ->with(['jobs', 'technicians','partProblems'])
            ->orderBy('date_in', 'desc')
            ->first();

        if (!$report) {
            return null;
        }

        // Calculate DT Time if both hour_in and hour_out exist
        $dtTime = null;
        if ($report->hour_in && $report->hour_out) {
            try {
                $hourIn = Carbon::createFromFormat('H:i', $report->hour_in);
                $hourOut = Carbon::createFromFormat('H:i', $report->hour_out);

                // Hitung durasi positif berapapun kondisinya
                $dtTime = abs($hourOut->diffInMinutes($hourIn));
            } catch (\Exception $e) {
                $dtTime = null;
            }
        } else {
            $dtTime = null;
        }

        return [
            'daily_report_id' => $report->daily_report_id,
            'date_in' => $report->date_in->format('d/m/Y'),
            'hm' => $report->hm,
            'problem' => $report->problem,
            'problem_component' => $report->problem_component,
            'problem_description' => $report->problem_description,
            'shift' => $report->shift,
            'hour_in' => $report->hour_in,
            'hour_out' => $report->hour_out,
            'dt_time' => $dtTime,
            'jobs' => $report->jobs->pluck('job_description')->toArray(),
            'technicians' => $report->technicians->pluck('name')->toArray(),
            'plan_fix' => $report->plan_fix,
            'plan_rekomen' => $report->plan_rekomen,
            'Parts' => $report->partProblems->map(function ($item) {
                return [
                    'part_name' => $item->part_name,
                    'part_code' => $item->code_part,
                ];
            })->toArray(),
        ];
    }

    /**
     * Get open backlogs for unit
     */
    private function getOpenBacklogs($unitCode)
    {
        return Backlog::where('unit_code', $unitCode)
            ->where('status', 'OPEN')
            ->with(['backlogParts.part'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($backlog) {
                return [
                    'id' => $backlog->id,
                    'problem_description' => $backlog->problem_description,
                    'backlog_job' => $backlog->backlog_job,
                    'hm_found' => $backlog->hm_found,
                    'plan_hm' => $backlog->plan_hm,
                    'notes' => $backlog->notes,
                    'created_at' => $backlog->created_at->format('d/m/Y'),
                    'plan_pull_date' => $backlog->plan_pull_date ? $backlog->plan_pull_date->format('d/m/Y H:i') : null,
                    'parts' => $backlog->backlogParts->map(function ($bp) {
                        return [
                            'part_code' => $bp->part_code,
                            'part_name' => $bp->part->part_name ?? 'Unknown',
                            'quantity' => $bp->quantity
                        ];
                    })
                ];
            });
    }

    /**
     * Get closed backlogs for unit
     */
    private function getClosedBacklogs($unitCode)
    {
        return Backlog::where('unit_code', $unitCode)
            ->where('status', 'CLOSED')
            ->with(['backlogParts.part'])
            ->orderBy('updated_at', 'desc')
            ->get()
            ->map(function ($backlog) {
                return [
                    'id' => $backlog->id,
                    'problem_description' => $backlog->problem_description,
                    'backlog_job' => $backlog->backlog_job,
                    'hm_found' => $backlog->hm_found,
                    'plan_hm' => $backlog->plan_hm,
                    'created_at' => $backlog->created_at->format('d/m/Y'),
                    'updated_at' => $backlog->updated_at->format('d/m/Y'),
                    'plan_pull_date' => $backlog->plan_pull_date ? $backlog->plan_pull_date->format('d/m/Y H:i') : null,
                    'parts' => $backlog->backlogParts->map(function ($bp) {
                        return [
                            'part_code' => $bp->part_code,
                            'part_name' => $bp->part->part_name ?? 'Unknown',
                            'quantity' => $bp->quantity
                        ];
                    })
                ];
            });
    }

    /**
     * Get part usage statistics from daily reports
     */
    private function getPartUsageStats($unitId)
    {
        $stats = DB::table('daily_reports as dr')
            ->join('part_problems as pp', 'dr.daily_report_id', '=', 'pp.daily_report_id')
            ->where('dr.unit_id', $unitId)
            ->select('pp.code_part', 'pp.part_name', DB::raw('SUM(pp.quantity) as total_quantity'))
            ->groupBy('pp.code_part', 'pp.part_name')
            ->orderBy('total_quantity', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($stat) {
                return [
                    'part_code' => $stat->code_part,
                    'part_name' => $stat->part_name,
                    'total_quantity' => $stat->total_quantity
                ];
            });

        return $stats;
    }

    /**
     * Get monthly usage statistics
     */
    private function getMonthlyUsageStats($unitId)
    {
        $stats = DailyReport::where('unit_id', $unitId)
            ->select(
                DB::raw('YEAR(date_in) as year'),
                DB::raw('MONTH(date_in) as month'),
                DB::raw('COUNT(*) as report_count')
            )
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get()
            ->map(function ($stat) {
                return [
                    'period' => Carbon::create($stat->year, $stat->month)->format('M Y'),
                    'count' => $stat->report_count
                ];
            });

        return $stats->reverse()->values();
    }

    /**
     * Get service component statistics - Problem Component Analysis
     * Only shows components from UNSCHEDULED services
     */
    private function getServiceComponentStats($unitId)
    {
        $stats = DB::table('daily_reports as dr')
            ->join('part_problems as pp', 'dr.daily_report_id', '=', 'pp.daily_report_id')
            ->where('dr.problem', 'UNSCHEDULLE')
            ->where('dr.unit_id', $unitId)
            ->select(
                'pp.code_part',
                'pp.part_name',
                DB::raw('SUM(pp.quantity) as total_quantity')
            )
            ->groupBy('pp.code_part', 'pp.part_name')
            ->orderByDesc('total_quantity')
            ->get()
            ->map(function ($stat) {
                return [
                    'code_part' => $stat->code_part,
                    'part_name' => $stat->part_name,
                    'quantity' => $stat->total_quantity
                ];
            });

        return $stats;
    }

    /**
     * Get detailed daily report
     */
    public function getDailyReportDetail(Request $request)
    {
        $reportId = $request->get('report_id');

        $report = DailyReport::with(['unit', 'jobs', 'technicians', 'partProblems'])
            ->where('daily_report_id', $reportId)
            ->first();

        if (!$report) {
            return response()->json([
                'success' => false,
                'message' => 'Daily report not found'
            ], 404);
        }

        // Check if report belongs to current site
        if ($report->unit->site_id !== session('site_id')) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to report'
            ], 403);
        }

        // Calculate DT Time
        $dtTime = null;
        if ($report->hour_in && $report->hour_out) {
            try {
                $hourIn = Carbon::createFromFormat('H:i', $report->hour_in);
                $hourOut = Carbon::createFromFormat('H:i', $report->hour_out);

                // Hitung durasi positif berapapun kondisinya
                $dtTime = abs($hourOut->diffInMinutes($hourIn));
            } catch (\Exception $e) {
                $dtTime = null;
            }
        } else {
            $dtTime = null;
        }


        $data = [
            'daily_report_id' => $report->daily_report_id,
            'unit_code' => $report->unit->unit_code,
            'date_in' => $report->date_in->format('d/m/Y'),
            'hm' => $report->hm,
            'problem' => $report->problem,
            'problem_component' => $report->problem_component,
            'problem_description' => $report->problem_description,
            'shift' => $report->shift,
            'hour_in' => $report->hour_in,
            'hour_out' => $report->hour_out,
            'dt_time' => $dtTime,
            'plan_fix' => $report->plan_fix,
            'plan_rekomen' => $report->plan_rekomen,
            'lifetime_component' => $report->lifetime_component,
            'jobs' => $report->jobs->pluck('job_description')->toArray(),
            'technicians' => $report->technicians->pluck('name')->toArray(),
            'Parts' => $report->partProblems->map(function ($item) {
                return [
                    'part_name' => $item->part_name,
                    'part_code' => $item->code_part,
                ];
            })->toArray(),


            // 'jobs' => $report->jobs->map(function ($job) {
            //     return [
            //         'job_description_id' => $job->job_description_id,
            //         'job_description' => $job->job_description,
            //         'highlight' => $job->highlight
            //     ];
            // })
        ];

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
}
