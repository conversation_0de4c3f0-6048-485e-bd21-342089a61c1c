@extends('sites.content')
@section('title', 'Dashboard Unit Site')
@section('contentsite')

    <div class="min-h-screen bg-gray-100">
        <div class="bg-white shadow-sm mb-1">
            <div class="w-full mx-auto px-6 py-4">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div>
                        <h1 class="text-2xl font-semibold text-gray-900" style="font-size: 1.5rem !important;">Dashboard
                            Kinerja Unit</h1>
                    </div>
                    <div class="relative w-full md:w-72"> <input type="text" id="unit-search-input"
                            class="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition"
                            placeholder="Ketik kode unit..." autocomplete="off" spellcheck="false">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        <div id="unit-search-results"
                            class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md py-1 max-h-60 overflow-auto hidden">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="loading-state" class="w-full px-4 py-12 hidden">
            <div class="flex flex-col items-center justify-center py-12">
                <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4"></div>
                <p class="text-gray-600 text-lg">Memuat data unit...</p>
            </div>
        </div>

        <div id="empty-state" class="w-full px-4 py-16" style="background-color: #ffffff; height: 1200px;">
            <div class="flex flex-col items-center justify-center text-center py-12 relative overflow-hidden">

                <!-- Optional: Angin Background -->
                <div class="absolute top-0 left-0 w-full h-full pointer-events-none">
                    <div class="wind-swoosh delay-0"></div>
                    <div class="wind-swoosh delay-2"></div>
                    <div class="wind-swoosh delay-4"></div>
                </div>

                <!-- Ganti Icon ke GIF -->
                <div class="mb-6 animate-float">
                    <img src="{{ asset('assets/template/checklist.gif') }}" alt="Checklist" class="w-40 h-40 opacity-80">
                </div>

                <h3 class="text-xl font-semibold text-gray-800 mb-2 animate-fadeIn">Belum Ada Unit yang Dipilih</h3>
                <p class="text-gray-500 max-w-lg animate-fadeIn delay-1">
                    Silakan cari dan pilih kode unit di atas untuk menampilkan dashboard kinerjanya.
                </p>
            </div>
        </div>

        <div id="dashboard-content" class="hidden w-full py-2 px-0">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="lg:col-span-2 space-y-6">
                    <div class="bg-white shadow-md rounded-xl overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 bg-blue-100 rounded-lg p-3"> <svg class="h-6 w-6 text-blue-600"
                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                </div>
                                <h3 class="ml-4 text-lg font-semibold text-gray-800 head2">Daftar Daily Report</h3>
                            </div>
                        </div>
                        <div class="px-6 pb-6">
                            <div class="overflow-x-auto border border-gray-200 rounded-lg">
                                <table class="min-w-full divide-y divide-gray-200" id="daily-reports-table">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th scope="col"
                                                class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Tanggal</th>
                                            <th scope="col"
                                                class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                HM</th>
                                            <th scope="col"
                                                class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Problem</th>
                                            <th scope="col"
                                                class="px-4 py-3 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                                                Shift</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="bg-white shadow-md rounded-xl overflow-hidden">
                            <div class="p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 bg-indigo-100 rounded-lg p-3">
                                        <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24"
                                            stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                                        </svg>
                                    </div>
                                    <h3 class="ml-4 text-lg font-semibold text-gray-800 head2">Analisis Komponen Problem
                                    </h3>
                                </div>
                            </div>
                            <div class="px-6 pb-6" id="problem-component-content">
                            </div>
                        </div>

                        <div class="bg-white shadow-md rounded-xl overflow-hidden">
                            <div class="p-6">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 bg-green-100 rounded-lg p-3">
                                        <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24"
                                            stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                        </svg>
                                    </div>
                                    <h3 class="ml-4 text-lg font-semibold text-gray-800 head2">Frekuensi per Bulan</h3>
                                </div>
                                <div class="px-6 pb-6">
                                    <canvas id="monthly-usage-chart" height="250"></canvas>
                                    <div id="monthly-usage-empty" class="text-center text-gray-500 py-8 hidden">
                                        <p>Belum ada data frekuensi bulanan</p>
                                    </div>
                                </div>
                            </div>
                            <hr class="my-2">
                            <h1 class="text-center font-bold text-uppercase pb-2">Penggunaan Part Service Pada Unit</h1>
                            <div class="px-6 pb-6" id="part-usage-content">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <div class="bg-white shadow-md rounded-xl overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 bg-purple-100 rounded-lg p-3">
                                    <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24"
                                        stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <h3 class="ml-4 text-lg font-semibold text-gray-800 head2">Riwayat Report</h3>
                            </div>
                        </div>
                        <div class="px-6 pb-6" id="latest-report-content">
                        </div>
                    </div>

                    <div class="bg-white shadow-md rounded-xl overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 bg-red-100 rounded-lg p-3">
                                    <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                </div>
                                <h3 class="ml-4 text-lg font-semibold text-gray-800 head2">Backlog OPEN</h3>
                            </div>
                        </div>
                        <div class="px-6 pb-6" id="open-backlogs-content">
                        </div>
                    </div>

                    <div class="bg-white shadow-md rounded-xl overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 bg-green-100 rounded-lg p-3">
                                    <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24"
                                        stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <h3 class="ml-4 text-lg font-semibold text-gray-800 head2">Backlog CLOSED</h3>
                            </div>
                        </div>
                        <div class="px-6 pb-6" id="closed-backlogs-content">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <div class="modal fade fixed inset-0 overflow-y-auto hidden" id="daily-report-modal" tabindex="-1"
        aria-labelledby="dailyReportModalLabel" aria-hidden="true">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-600 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div
                class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full max-w-5xl">

                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="flex justify-between items-start border-b border-gray-200 pb-2 mb-4">
                        <h3 class="text-xl leading-6 font-semibold text-gray-900" id="dailyReportModalLabel">Detail Daily
                            Report</h3>
                        <button type="button" onclick="hideDailyReportModal()" class="text-gray-400 hover:text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                                stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div class="mt-4" id="daily-report-modal-content">
                        <!-- Isi modal dinamis -->
                    </div>
                </div>

                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" onclick="hideDailyReportModal()"
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm transition">
                        Tutup
                    </button>
                </div>
            </div>
        </div>
    </div>
    </div>
    <script>
        function hideDailyReportModal() {
            document.getElementById('daily-report-modal').classList.add('hidden');
            // document.body.classList.remove('overflow-hidden');
        }
    </script>


    @vite(['resources/css/site/dashboard-unit-site.css'])
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    @vite(['resources/js/site/dashboard-unit-site-chart.js'])
    @vite(['resources/js/site/dashboard-unit-site.js'])
@endsection