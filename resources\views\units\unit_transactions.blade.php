@extends('sites.content')
@section('contentsite')
@section('title', 'Unit Transactions')
<!-- Loading overlay for file operations -->
<div class="file-loading-overlay" id="file-loading-overlay">
    <div class="file-loading-spinner">
        <div class="spinner"></div>
        <div class="text-center"></div>
    </div>
</div>

@push('styles')
<style>
    .status-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        font-weight: bold;
    }

    .status-on-process {
        background-color: #007bff;
        color: white;
    }

    .status-mr {
        background-color: #17a2b8;
        color: white;
    }

    .status-pending {
        background-color: #ffc107;
        color: black;
    }

    .status-ready {
        background-color: #28a745;
        color: white;
    }

    .status-completed {
        background-color: #6c757d;
        color: white;
    }

    /* Make attachment section more prominent for DH site */
    #attachment-section.col-md-12 .shadow-kit {
        border: 2px solid #28a745;
        box-shadow: 0 0.5rem 1rem rgba(40, 167, 69, 0.15) !important;
    }

    #attachment-section.col-md-12 .card-header {
        background-color: #28a745 !important;
        color: white !important;
    }

    #attachment-section.col-md-12 .card-header h6 {
        color: white !important;
        font-weight: bold !important;
    }

    /* Dropdown submenu styles for Bootstrap 5 */
    .dropdown-submenu {
        position: relative;
    }

    .dropdown-submenu .dropdown-menu {
        top: 0;
        left: 100%;
        margin-top: -1px;
    }

    .dropdown-submenu-menu {
        display: none;
        position: absolute;
        left: 100%;
        top: 0;
        z-index: 1000;
        min-width: 10rem;
        padding: 0.5rem 0;
        margin: 0 0 0 0.125rem;
        font-size: 1rem;
        color: #212529;
        text-align: left;
        list-style: none;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 0.25rem;
    }

    .dropdown-submenu>a:after {
        display: block;
        content: " ";
        float: right;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
        border-width: 5px 0 5px 5px;
        border-left-color: #ccc;
        margin-top: 5px;
        margin-right: -10px;
    }

    /* Part search dropdown styles */
    #part-search-results {
        z-index: 1050;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    #part-search-results .dropdown-item {
        padding: 10px 15px;
        border-bottom: 1px solid #f0f0f0;
    }

    #part-search-results .dropdown-item:hover,
    #part-search-results .dropdown-item.active {
        background-color: #f8f9fa;
        color: #212529;
    }

    #part-search-results .badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }

    /* Highlight effect for duplicate parts */
    .part-item {
        transition: background-color 0.3s ease;
    }

    .part-item.bg-secondary {
        transition: background-color 0.3s ease;
        background-color: #fff3cd !important;
    }

    /* Loading animation styles */
    .file-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        visibility: hidden;
        opacity: 0;
        transition: visibility 0s, opacity 0.3s linear;
    }

    .file-loading-overlay.active {
        visibility: visible;
        opacity: 1;
    }

    .file-loading-spinner {
        width: 120px;
        height: 120px;
        background-color: white;
        border-radius: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 20px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .file-loading-spinner .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 10px;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    /* Pagination styles */
    .pagination-wrapper {
        margin-top: 1.5rem !important;
        padding: 0.5rem;
        background-color: #f8f9fa;
        border-radius: 0.25rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .pagination-wrapper .pagination {
        margin-bottom: 0;
    }

    .pagination-wrapper .page-item .page-link {
        color: #007bff;
        background-color: #fff;
        border: 1px solid #dee2e6;
        padding: 0.375rem 0.75rem;
    }

    .pagination-wrapper .page-item.active .page-link {
        color: #fff;
        background-color: #007bff;
        border-color: #007bff;
    }

    .pagination-wrapper .page-item.disabled .page-link {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
        border-color: #dee2e6;
    }
</style>
@endpush

<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0">{{ session('name') }}</p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="p-2">
    <!-- Hidden input to store site ID for JavaScript -->
    <input type="hidden" id="current-site-id" value="{{ session('site_id') }}">
    <!-- Hidden input to store user name for JavaScript -->
    <input type="hidden" id="current-user-name" value="{{ session('name') }}">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="shadow-kit bgwhite">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center pt-2 pb-1 pl-2 pr-2">
                    <h5 class="mb-0 font-bold text-uppercase text-white">Units</h5>
                    <div class="d-flex">
                        <input type="text" id="unit-search" class="form-control form-control-sm" placeholder="Search unit code or type...">
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered w-100" id="units-table">
                            <thead class="bg-light">
                                <tr>
                                    <th>Unit Code</th>
                                    <th>Unit Type</th>
                                    <th>Site ID</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody id="units-table-body">
                                @foreach($units as $unit)
                                <tr>
                                    <td>{{ $unit->unit_code }}</td>
                                    <td>{{ $unit->unit_type }}</td>
                                    <td>{{ $unit->site_id }}</td>
                                    <td>
                                        <button class="btn btn-sm btn-primary btn-add-transaction" data-unit-id="{{ $unit->id }}"> <i class="fas fa-plus"></i> Add Out Stock
                                        </button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-1">
                        <div>
                            <span>Showing {{ $units->firstItem() ?? 0 }} to {{ $units->lastItem() ?? 0 }} of {{ $units->total() }} entries</span>
                        </div>
                        <div id="units-pagination-container">
                            {{ $units->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center pt-2 pb-1 pl-2 pr-2">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0 mr-3 font-bold text-uppercase text-white">Transaction History</h5>
                        <div class="dropdown ml-2">
                            <button class="btn btn-sm btn-light dropdown-toggle" type="button" id="exportSelectedDropdown" data-bs-toggle="dropdown" aria-expanded="false" disabled>
                                <i class="mdi mdi-file-pdf-box"></i> Export Selected (<span id="selected-count">0</span>)
                            </button>
                            <div class="dropdown-menu" aria-labelledby="exportSelectedDropdown">
                                <div class="dropdown-submenu">
                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Preview</a>
                                    <div class="dropdown-menu dropdown-submenu-menu">
                                        @if(session('site_id') !== 'DH')
                                        <a class="dropdown-item" href="#" id="preview-selected-outgoing">
                                            {{ session('site_id') == 'TRB' ? 'Preview DP FREON' : 'Preview SPB' }}
                                        </a>
                                        <a class="dropdown-item" href="#" id="preview-selected-report">Preview DO/BAPP</a>
                                        @endif
                                        @if(session('site_id') !== 'PPA' && session('site_id') !== 'DH')
                                        <a class="dropdown-item" href="#" id="preview-selected-slip">Preview SLIP STORE</a>
                                        @endif
                                        @if(session('site_id') === 'DH')
                                        <a class="dropdown-item" href="#" id="preview-selected-slip-store">Preview Slip Store</a>
                                        @endif
                                    </div>
                                </div>
                                <div class="dropdown-submenu @if (session('site_id') === 'TRB')d-none @endif">
                                    <a class="dropdown-item dropdown-toggle" href="#" data-bs-toggle="dropdown">Download</a>
                                    <div class="dropdown-menu dropdown-submenu-menu">
                                        @if(session('site_id') !== 'DH')
                                        <a class="dropdown-item" href="#" id="preview-selected-outgoing">
                                            {{ session('site_id') == 'TRB' ? 'Preview DP FREON' : 'Preview SPB' }}
                                        </a>
                                        <a class="dropdown-item" href="#" id="download-selected-report">Download DO/BAPP</a>
                                        @endif
                                        @if(session('site_id') !== 'PPA' && session('site_id') !== 'DH')<a class="dropdown-item" href="#" id="download-selected-slip">Download SLIP STORE</a>@endif
                                        @if(session('site_id') === 'PPA')<a class="dropdown-item" href="#" id="download-selected-excel">Download Excel (DO & PSB)</a>@endif
                                        @if(session('site_id') === 'DH')
                                        <a class="dropdown-item" href="#" id="download-selected-bapp">Download BAPP</a>
                                        <a class="dropdown-item" href="#" id="download-selected-slip-store">Download Slip Store</a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                        @if(session('site_id') === 'DH')
                        <button class="btn btn-sm btn-success ml-2" id="cetak-bapp-btn" disabled>
                            <i class="mdi mdi-file-document"></i> Preview BAPP
                        </button>
                        @endif
                    </div>
                    <div class="d-flex flex-wrap">
                        <div class="d-flex mb-2 mr-2">
                            <input type="date" id="date-from" class="form-control form-control-sm mr-1" title="From Date">
                            <span class="align-self-center mx-1">to</span>
                            <input type="date" id="date-to" class="form-control form-control-sm ml-1 mr-2" title="To Date">
                        </div>
                        <div class="d-flex">
                            <input type="text" id="transaction-search" class="form-control form-control-sm mr-2" placeholder="Search unit code or type...">
                            <select id="status-filter" class="form-control form-control-sm mr-2">
                                <option value="">All Status</option>
                                <option value="selesai">Selesai</option>
                                <option value="On Process">On Process</option>
                                <option value="MR">MR</option>
                                <option value="Ready">Ready</option>
                                <option value="Ready WO">Ready WO</option>
                                <option value="Ready PO">Ready PO</option>
                                <option value="Pending">Pending</option>
                            </select>
                            <select id="unit-filter" class="form-control form-control-sm">
                                <option value="">All Units</option>
                                @foreach($units as $unit)
                                <option value="{{ $unit->id }}">{{ $unit->unit_code }} - {{ $unit->unit_type }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered w-100" id="transactions-table">
                            <thead class="bg-light">
                                @if(session('site_id') === 'DH')
                                <!-- DH site specific columns -->
                                <tr>
                                    <th><input class="form-check-input m-0 p-0" style="position: relative !important;" type="checkbox" id="select-all-checkbox">
                                    </th>
                                    <th>No</th>
                                    <th>Unit</th>
                                    <th>Status</th>
                                    <th>No. IREQ</th>
                                    <th>Tanggal MR</th>
                                    <th>Nomor PO</th>
                                    <th>BAPP Number</th>
                                    <th>
                                        Aksi
                                    </th>
                                </tr>
                                @else
                                <!-- Default columns for other sites -->
                                <tr>
                                    <th><input class="form-check-input m-0 p-0" style="position: relative !important;" type="checkbox" id="select-all-checkbox">
                                    </th>
                                    <th>No</th>
                                    <th>Unit</th>
                                    <th>Status</th>
                                    <th>{{ session('site_id') === 'IMK' ? 'NO PROJECT' : 'Nomor WO' }}</th>
                                    <th>{{ session('site_id') === 'IMK' ? 'Nomor MR' : 'Nomor PO' }}</th>
                                    @if(session('site_id') === 'IMK')
                                    <th>Issue Nomor</th>
                                    @endif
                                    <th>{{ session('site_id') === 'IMK' ? 'Item Code' : 'Nomor DO' }}</th>
                                    @if(session('site_id') === 'PPA')
                                    <th>Tanggal MR</th>
                                    <th>Tanggal DO</th>
                                    <th>Tanggal PO</th>
                                    @endif
                                    <th>Keterangan</th>
                                    <th>Aksi</th>
                                </tr>
                                @endif
                            </thead>
                            <tbody id="transactions-table-body">
                            </tbody>
                        </table>
                        <div id="pagination-container" class="mt-3 d-flex justify-content-center pagination-wrapper">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Modal -->
<div class="modal fade" id="transactionModal" tabindex="-1" aria-labelledby="transactionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document" style="max-width: 98% !important;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="transactionModalLabel">Part Out Unit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="transactionForm">
                    @csrf
                    <input type="hidden" name="unit_id" id="unit_id">
                    <input type="hidden" name="transaction_id" id="transaction_id">

                    <!-- Top section with unit info and form fields in horizontal layout -->
                    <div class="row mb-3">
                        <!-- Unit Information Card -->
                        <div class="col-md-3">
                            <div class="shadow-kit h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0 font-bold text-uppercase">Unit Information</h6>
                                </div>
                                <div class="card-body" id="unit-info">
                                    <!-- Unit information will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Form Fields in 3 columns -->
                        <div class="col-md-9">
                            <div class="shadow-kit h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0 font-bold text-uppercase">Transaction Details</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <!-- Column 1: Status and Dates -->
                                        <div class="col-md-4">
                                            <div class="shadow-kit mb-2">
                                                <div class="card-header bg-light py-1">
                                                    <h6 class="mb-0 text-uppercase small">Status & Tanggal</h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <div class="row g-2">
                                                        <div class="col-md-6">
                                                            <label for="status" class="form-label small mb-1">Status</label>
                                                            <select name="status" id="status" class="btn btn-sm btn-primary form-select-sm">
                                                                <option value="On Process">On Process</option>
                                                                <option value="MR">MR</option>
                                                                <option value="Pending">Pending</option>
                                                                @if(session('site_id') !== 'UDU')
                                                                <option value="Ready WO">Ready WO</option>
                                                                @endif
                                                                <option value="Ready PO">Ready PO</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-md-6 @if (session('site_id') === 'TRB')d-none @endif">
                                                            <label for="updated_at" class="form-label small mb-1">Tanggal Update</label>
                                                            <input type="date" name="updated_at" id="updated_at" class="form-control form-control-sm">
                                                        </div>
                                                        <div class="col-md-6 @if (session('site_id') === 'TRB')d-none @endif">
                                                                <label for="mr_date" class="form-label small mb-1">Tanggal MR</label>
                                                                <input type="date" name="mr_date" id="mr_date" class="form-control form-control-sm">
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label for="po_date" class="form-label small mb-1">Tanggal PO</label>
                                                            <input required type="date" name="po_date" id="po_date" class="form-control form-control-sm">
                                                        </div>
                                                        <div class="col-md-6">
                                                            @if(session('site_id') !== 'TRB')
                                                            <label for="do_date" class="form-label small mb-1">Tanggal DO</label>
                                                            @else
                                                            <label for="do_date" class="form-label small mb-1">Tanggal BAPP</label>
                                                            @endif
                                                            <input type="date" name="do_date" id="do_date" class="form-control form-control-sm">
                                                        </div>
                                                        @if (session('site_id') === 'TRB')
                                                        <div class="col-md-6">
                                                            <label for="po_date" class="form-label small mb-1">Tanggal PO</label>
                                                            <input required type="date" name="po_date" id="po_date" class="form-control form-control-sm">
                                                        </div>
                                                        @endif
                                                        <div class="col-md-6">
                                                            <label for="po_date" class="form-label small mb-1">Tanggal PO</label>
                                                            <input required type="date" name="po_date" id="po_date" class="form-control form-control-sm">
                                                        </div>
                                                        @if(session('site_id') === 'DH')
                                                        <div class="col-md-12 mt-2">
                                                            <div class="mb-0">
                                                                <div class="card-header bg-light py-1">
                                                                    <h6 class="mb-0 text-uppercase small">DH Site Fields</h6>
                                                                </div>
                                                                <div class="card-body py-2">    
                                                                    <div class="row g-2">
                                                                        <div class="col-md-12">
                                                                            <label for="noireq" class="form-label small mb-1">No. IREQ (Request Number)</label>
                                                                            <input type="text" name="noireq" id="noireq" class="form-control form-control-sm">
                                                                        </div>
                                                                        <!-- Hidden fields for tanggalstart and tanggalend -->
                                                                        <input type="hidden" name="tanggalstart" id="tanggalstart">
                                                                        <input type="hidden" name="tanggalend" id="tanggalend">
                                                                        <!-- Hidden fields for DH site -->
                                                                        <input type="hidden" name="updated_at" id="updated_at_hidden">
                                                                        <input type="hidden" name="do_date" id="do_date_hidden">
                                                                        <input type="hidden" name="po_date" id="po_date_hidden">
                                                                        <input type="hidden" name="do_number" id="do_number_hidden">
                                                                        <input type="hidden" name="noSPB" id="noSPB_hidden">
                                                                        <input type="hidden" name="pekerjaan" id="pekerjaan_hidden">
                                                                        <input type="hidden" name="HMKM" id="HMKM_hidden">
                                                                        <input type="hidden" name="SHIFT" id="SHIFT_hidden">
                                                                        <input type="hidden" name="LOKASI" id="LOKASI_hidden">
                                                                        <input type="hidden" name="remarks" id="remarks_hidden">
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                        <!-- Column 2: Document Numbers -->
                                        <div class="col-md-4">
                                            <div class="shadow-kit mb-2">
                                                <div class="card-header bg-light py-1">
                                                    <h6 class="mb-0 small">Dokumen & Nomor</h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <!-- Document Numbers -->
                                                    <div id="wo_number_container" style="display: none;" class="form-group mb-2">
                                                        <label for="wo_number" class="form-label small mb-1">
                                                            {{ session('site_id') === 'PPA' ? 'WO number' : (session('site_id') === 'UDU' ? 'No.WO' : (session('site_id') === 'IMK' ? 'No Project' : '')) }}
                                                        </label>
                                                        <input type="text" name="wo_number" id="wo_number" class="form-control form-control-sm">
                                                    </div>

                                                    <div id="po_number_container" style="display: none;" class="form-group mb-2" @if(session('site_id')==='IMK' ) style="display:none !important;" @endif>
                                                        <label for="po_number" class="form-label small mb-1">
                                                            {{ session('site_id') === 'PPA' ? 'PO number' : (session('site_id') === 'UDU' ? 'NO. QTN' : 'Nomor PO') }}
                                                        </label>
                                                        <input type="text" name="po_number" id="po_number" class="form-control form-control-sm">
                                                    </div>

                                                    @if(session('site_id') === 'IMK')
                                                    <div id="issue_nomor_container" class="form-group mb-2">
                                                        <label for="issue_nomor" class="form-label small mb-1">Issue Nomor</label>
                                                        <input type="text" name="issue_nomor" id="issue_nomor" class="form-control form-control-sm">
                                                    </div>
                                                    @endif
                                                    <div class="row g-2">
                                                        <div class="col-md-6" @if(session('site_id')==='IMK' ) style="display:none;" @endif>
                                                            <div id="do_number_container" class="form-group mb-2">
                                                                <label for="do_number" class="form-label small mb-1">DO/BAPP/Dokumen</label>
                                                                <input type="text" name="do_number" id="do_number" class="form-control form-control-sm" placeholder="DOC-PWB/2025/IV/001">
                                                            </div>
                                                        </div>

                                                        <div class="col-md-6 ml-2 @if (session('site_id') === 'TRB')d-none @endif">
                                                            <div id="noSPB_container" class="form-group mb-2">
                                                                <label for="do_number" class="form-label small mb-1">
                                                                    {{-- Cek kondisi site_id untuk menentukan label --}}
                                                                    @if (session('site_id') === 'IMK')
                                                                    no.MR
                                                                    @elseif (session('site_id') === 'PPA')
                                                                    SPB Number
                                                                    @elseif (session('site_id') === 'UDU')
                                                                    REFF
                                                                    @else
                                                                    SPB Number
                                                                    @endif
                                                                </label>
                                                                <input type="text" name="noSPB" id="noSPB" class="form-control form-control-sm" placeholder="SPB/SSS-PPA/2025/001">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Column 3: Work Information and Contact -->
                                        <div class="col-md-4 @if (session('site_id') === 'TRB') d-none @endif">
                                            <div class="shadow-kit mb-2">
                                                <div class="card-header bg-light py-1">
                                                    <h6 class="mb-0 small">Informasi Pekerjaan</h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <div class="row g-2">
                                                        <div class="col-md-6">
                                                            <div class="form-group mb-2">
                                                                <label for="pekerjaan" class="form-label small mb-1">Pekerjaan</label>
                                                                <input type="text" name="pekerjaan" id="pekerjaan" class="form-control form-control-sm">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group mb-2">
                                                                <label for="HMKM" class="form-label small mb-1">HM / KM</label>
                                                                <input type="text" name="HMKM" id="HMKM" class="form-control form-control-sm">
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row g-2">
                                                        <div class="col-md-6">
                                                            <div class="form-group mb-2">
                                                                <label for="SHIFT" class="form-label small mb-1">SHIFT</label>
                                                                <input type="text" name="SHIFT" id="SHIFT" class="form-control form-control-sm">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group mb-2">
                                                                <label for="LOKASI" class="form-label small mb-1">LOKASI</label>
                                                                <input type="text" name="LOKASI" id="LOKASI" class="form-control form-control-sm">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Second row of form fields -->
                                    <div class="row mt-2">
                                        <!-- Contact Information -->
                                        <div class="col-md-4">
                                            <div class="shadow-kit mb-2">
                                                <div class="card-header bg-light py-1">
                                                    <h6 class="mb-0 small">Kontak & Customer</h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <div class="row g-2">
                                                        <div class="col-md-6">
                                                            <div class="form-group mb-2">
                                                                <label for="contact" class="form-label small mb-1">Contact Person</label>
                                                                <input type="text" name="contact" id="contact" class="form-control form-control-sm" value="{{ session('name') }}">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group mb-2">
                                                                <label for="phone" class="form-label small mb-1">Phone Number</label>
                                                                <input type="text" name="phone" id="phone" class="form-control form-control-sm" placeholder="081234567890">
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="row g-2">
                                                        <div class="col-md-6">
                                                            <div class="form-group mb-2">
                                                                <label for="customer" class="form-label small mb-1">Customer</label>
                                                                <input type="text" name="customer" id="customer" class="form-control form-control-sm" value="{{ session('site_id') === 'PPA' ? 'PT PUTRA PERKASA ABADI' : (session('site_id') === 'UDU' ? 'PT. Unggul Dinamika Utama' : (session('site_id') === 'IMK' ? 'Indo Muro Kencana' : (session('site_id') === 'DH' ? 'PT. DH SITE KINTAP' : session('site_id')))) }}">
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group mb-2">
                                                                <label for="sitework" class="form-label small mb-1">Site Work</label>
                                                                <input type="text" name="sitework" id="sitework" class="form-control form-control-sm">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Notes and Remarks -->
                                        <div class="col-md-4">
                                            <div class="shadow-kit mb-2">
                                                <div class="card-header bg-light py-1">
                                                    <h6 class="mb-0 small">Keterangan</h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <div class="form-group mb-2">
                                                        <label for="remarks" class="form-label small mb-1">Keterangan</label>
                                                        <textarea name="remarks" id="remarks" class="form-control form-control-sm" rows="3" style="height: 90px;">{{ session('site_id') === 'PPA' ? 'MAINTENANCE AC' : '' }}</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Attachment - Always visible for all sites -->
                                        <div class="col-md-4" id="attachment-section">
                                            <div class="shadow-kit mb-2">
                                                <div class="card-header bg-light py-1">
                                                    <h6 class="mb-0 small">Lampiran</h6>
                                                </div>
                                                <div class="card-body py-2">
                                                    <div class="form-group mb-2">
                                                        <label for="attachment" class="form-label small mb-1">Lampiran (Maks. 5MB)</label>
                                                        <input type="file" name="attachment" id="attachment" class="form-control dropify" data-max-file-size="5M" data-allowed-file-extensions="pdf jpg jpeg png doc docx xls xlsx">
                                                        <input type="hidden" id="current-attachment" name="current_attachment" value="">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Bottom section with parts list (full width) -->
                    <div class="row">
                        <div class="col-12">
                            <div class="shadow-kit p-2">
                                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0 font-bold text-uppercase">Parts List</h6>
                                    <div id="add-part-container" class="d-flex align-items-center" style="display: none;">
                                        <div class="input-group">
                                            <input style="width: 500px;" type="text" id="part-search" class="form-control" placeholder="Search part..." autocomplete="off">
                                            <div id="part-search-results" class="dropdown-menu" style="display: none; max-height: 300px; max-width: 500px; overflow-y: auto;">
                                                <!-- Search results will be displayed here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Header row for parts list -->
                                    <div class="row mb-2 font-weight-bold">
                                        <div class="col-md-5">Part Name</div>
                                        <div class="col-md-1">Stock</div>
                                        <div class="col-md-1">Quantity</div>
                                        <div class="col-md-3">Price</div>
                                        <div class="col-md-1">EUM</div>
                                        <div class="col-md-1">Action</div>
                                    </div>
                                    <div id="partsList" class="mb-0" style="max-height: 300px; overflow-y: auto;">
                                        <!-- Parts will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="text-right mt-3">
                        <a target="_blank" href="https://www.ilovepdf.com/compress_pdf" class="btn btn-link" title="Jika File Terlalu besar, Silahkan compress disini">Compres PDF</a>
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" id="submit-btn">Save Transaction</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Details Modal -->
<div class="modal fade" id="transactionDetailsModal" tabindex="-1" aria-labelledby="transactionDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document" style="min-width: 45% !important;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="transactionDetailsModalLabel">Out Part Units Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="transaction-details-content">
                <!-- Transaction details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="edit-transaction-btn">Edit</button>
            </div>
        </div>
    </div>
</div>
<!-- BAPP Modal -->
<div class="modal fade" id="bappModal" tabindex="-1" aria-labelledby="bappModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-md" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bappModalLabel">Preview BAPP</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="bappForm">
                    @csrf
                    <input type="hidden" name="unit_transaction_ids" id="bapp_unit_transaction_ids">

                    <div class="form-group mb-3">
                        <label for="tanggalstart" class="form-label">Tanggal Start</label>
                        <input type="date" class="form-control" id="bapp_tanggalstart" name="tanggalstart" required>
                    </div>

                    <div class="form-group mb-3">
                        <label for="tanggalend" class="form-label">Tanggal End</label>
                        <input type="date" class="form-control" id="bapp_tanggalend" name="tanggalend" required>
                    </div>

                    <div class="form-group mb-3">
                        <label for="noBA" class="form-label">Nomor BA</label>
                        <input type="text" class="form-control" id="bapp_noBA" name="noBA" required>
                    </div>

                    <div class="text-right mt-3">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                        <button type="button" class="btn btn-primary" id="preview-bapp-btn">Preview BAPP</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Attachment Preview Modal -->
<div class="modal fade" id="attachmentPreviewModal" tabindex="-1" aria-labelledby="attachmentPreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document" style="max-width: 70% !important;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="attachmentPreviewModalLabel">Lampiran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="attachment-preview-content">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">.</span>
                    </div>
                    <p class="mt-2">Memuat lampiran...</p>
                </div>
                <div id="attachment-preview-container" class="d-none">
                    <!-- Attachment preview will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <a href="#" class="btn btn-primary" id="download-attachment-btn" target="_blank">Unduh</a>
            </div>
        </div>
    </div>
</div>

@endsection
@section('resourcesite')
<!-- Compatibility script for require/define polyfills -->
@vite('resources/js/compatibility.js')

<!-- Make sure bootstrap is loaded before UnitTransactions.js -->
@vite('resources/js/bootstrap-init.js')
@vite('resources/js/bootstrap-dropdown-fix.js')
@vite('resources/js/units/UnitTransactions.js')

<!-- Dropify for file uploads -->
<link href="{{ asset('assets/libs/dropify/dropify.min.css') }}" rel="stylesheet" type="text/css" />
<script src="{{ asset('assets/libs/dropify/dropify.min.js') }}"></script>
<script src="{{ asset('assets/js/pages/form-fileuploads.init.js') }}"></script>

@endsection