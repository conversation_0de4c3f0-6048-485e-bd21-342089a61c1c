/**
 * Remainder Tasks and Requirements Management
 * Handles the display and interaction of upcoming service tasks and requirements
 */

document.addEventListener("DOMContentLoaded", function () {
    console.log("Remainder tasks script loaded");
    // Initialize remainder tasks functionality
    initRemainderTasks();
});

function initRemainderTasks() {
    console.log("Initializing remainder tasks");
    // Load initial data
    loadRemainderTasksData();

    // Set up event listeners
    setupRemainderEventListeners();
}

function setupRemainderEventListeners() {
    // Filter button click
    const filterBtn = document.getElementById("remainderFilterBtn");
    if (filterBtn) {
        filterBtn.addEventListener("click", function (e) {
            e.preventDefault();
            loadRemainderTasksData();
            return false;
        });
    }

    // Date input changes
    const startDateInput = document.getElementById("remainderFilterStart");
    const endDateInput = document.getElementById("remainderFilterEnd");

    if (startDateInput) {
        startDateInput.addEventListener("change", function () {
            loadRemainderTasksData();
        });
    }

    if (endDateInput) {
        endDateInput.addEventListener("change", function () {
            loadRemainderTasksData();
        });
    }

    // Table row click handler (event delegation)
    const tableBody = document.getElementById("remainderTaskTableBody");
    if (tableBody) {
        tableBody.addEventListener("click", function (e) {
            const row = e.target.closest("tr");
            if (row && row.dataset.unitId) {
                e.preventDefault();
                showRemainderTaskDetail(
                    row.dataset.unitId,
                    row.dataset.source,
                    row.dataset.sourceId
                );
                return false;
            }
        });
    }
}

function loadRemainderTasksData() {
    const startDate = document.getElementById("remainderFilterStart")?.value;
    const endDate = document.getElementById("remainderFilterEnd")?.value;

    if (!startDate || !endDate) {
        console.error("Start date and end date are required");
        return;
    }

    // Show loading indicator
    showRemainderLoading(true);

    // Make AJAX request
    fetch("/sites/remainder-tasks/data", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN":
                document
                    .querySelector('meta[name="csrf-token"]')
                    ?.getAttribute("content") || "",
        },
        body: JSON.stringify({
            start_date: startDate,
            end_date: endDate,
        }),
    })
        .then((response) => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then((data) => {
            if (data.success) {
                renderRemainderTasksTable(data.data);
            } else {
                console.error("Error loading remainder tasks:", data.message);
                showRemainderError(
                    data.message || "Terjadi kesalahan saat memuat data"
                );
            }
        })
        .catch((error) => {
            console.error("Error loading remainder tasks:", error);
            showRemainderError("Terjadi kesalahan saat memuat data");
        })
        .finally(() => {
            showRemainderLoading(false);
        });
}

function renderRemainderTasksTable(data) {
    const tableBody = document.getElementById("remainderTaskTableBody");
    const emptyState = document.getElementById("remainderEmptyState");

    if (!tableBody) return;

    // Clear existing content
    tableBody.innerHTML = "";

    if (!data || data.length === 0) {
        emptyState.style.display = "block";
        return;
    }

    emptyState.style.display = "none";

    data.forEach((item) => {
        const row = document.createElement("tr");
        row.className = "remainder-task-row";
        row.style.cursor = "pointer";
        row.dataset.unitId = item.unit_id || "";
        row.dataset.source = item.source || "";
        row.dataset.sourceId = item.source_id || "";

        // Format service date
        const serviceDate = formatRemainderDate(item.service_date);

        // Create parts list HTML
        const partsHtml = createPartsListHtml(item.parts);

        // Create stock info HTML
        const stockHtml = createStockInfoHtml(item.parts);

        row.innerHTML = `
            <td class="fw-bold">${escapeHtml(item.unit_name || "")}</td>
            <td>${serviceDate}</td>
            <td>${item.hm_service || "-"}</td>
            <td>
                <span class="badge ${
                    item.source === "Backlog" ? "bg-warning" : "bg-info"
                } text-dark">
                    ${escapeHtml(item.source || "")}
                </span>
            </td>
            <td>${partsHtml}</td>
            <td>${stockHtml}</td>
        `;

        // Add hover effect
        row.addEventListener("mouseenter", function () {
            this.classList.add("table-active");
        });

        row.addEventListener("mouseleave", function () {
            this.classList.remove("table-active");
        });

        tableBody.appendChild(row);
    });
}

function createPartsListHtml(parts) {
    if (!parts || parts.length === 0) {
        return '<span class="text-muted">-</span>';
    }

    return parts
        .map((part) => {
            return `<div class="mb-1">
            <small class="fw-bold">${escapeHtml(part.part_name || "")}</small>
            ${
                part.quantity
                    ? `<span class="text-muted"> (${part.quantity})</span>`
                    : ""
            }
        </div>`;
        })
        .join("");
}

function createStockInfoHtml(parts) {
    if (!parts || parts.length === 0) {
        return '<span class="text-muted">-</span>';
    }

    return parts
        .map((part) => {
            const stockClass = getStockStatusClass(
                part.stock_quantity,
                part.min_stock
            );
            return `<div class="mb-1">
            <span class="badge ${stockClass}">${part.stock_quantity || 0}</span>
        </div>`;
        })
        .join("");
}

function getStockStatusClass(stock, minStock) {
    const stockNum = parseInt(stock) || 0;
    const minStockNum = parseInt(minStock) || 0;

    if (stockNum === 0) {
        return "bg-danger";
    } else if (stockNum <= minStockNum) {
        return "bg-warning text-dark";
    } else {
        return "bg-success";
    }
}

function showRemainderTaskDetail(unitId, source, sourceId) {
    const modalElement = document.getElementById("remainderDetailModal");
    const modalBody = document.getElementById("remainderDetailModalBody");
    const modalTitle = document.getElementById("remainderDetailModalLabel");

    // Check if Bootstrap is available
    if (typeof bootstrap === "undefined") {
        console.error("Bootstrap is not loaded");
        return;
    }

    const modal = new bootstrap.Modal(modalElement);

    // Set loading state
    modalBody.innerHTML =
        '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">Memuat detail...</p></div>';
    modalTitle.textContent = "Detail Tugas dan Kebutuhan";

    // Show modal
    modal.show();

    // Load detail data
    fetch("/sites/remainder-tasks/detail", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "X-CSRF-TOKEN":
                document
                    .querySelector('meta[name="csrf-token"]')
                    ?.getAttribute("content") || "",
        },
        body: JSON.stringify({
            unit_id: unitId,
            source: source,
            source_id: sourceId,
        }),
    })
        .then((response) => response.json())
        .then((data) => {
            if (data.success) {
                renderRemainderTaskDetail(data.data, modalBody, modalTitle);
            } else {
                modalBody.innerHTML = `<div class="alert alert-danger">Error: ${
                    data.message || "Terjadi kesalahan"
                }</div>`;
            }
        })
        .catch((error) => {
            console.error("Error loading detail:", error);
            modalBody.innerHTML =
                '<div class="alert alert-danger">Terjadi kesalahan saat memuat detail</div>';
        });
}

function renderRemainderTaskDetail(data, modalBody, modalTitle) {
    modalTitle.textContent = `Detail ${data.source} - ${data.unit_name}`;

    let html = `
        <div class="row">
            <div class="col-md-6">
                <h6 class="font-bold mb-2">Informasi Unit</h6>
                <table class="table table-sm">
                    <tr><td><strong>Nama Unit:</strong></td><td>${escapeHtml(
                        data.unit_name || ""
                    )}</td></tr>
                    <tr><td><strong>Unit Code:</strong></td><td>${escapeHtml(
                        data.unit_code || ""
                    )}</td></tr>
                    <tr><td><strong>Sumber:</strong></td><td><span class="badge ${
                        data.source === "Backlog" ? "bg-warning" : "bg-info"
                    } text-dark">${escapeHtml(
        data.source || ""
    )}</span></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6 class="font-bold mb-2">Informasi Service</h6>
                <table class="table table-sm">
                    <tr><td><strong>Tanggal Service:</strong></td><td>${formatRemainderDate(
                        data.service_date
                    )}</td></tr>
                    <tr><td><strong>HM Service:</strong></td><td>${
                        data.hm_service || "-"
                    }</td></tr>
                    <tr><td><strong>Status:</strong></td><td>${escapeHtml(
                        data.status || ""
                    )}</td></tr>
                </table>
            </div>
        </div>
    `;

    if (data.source === "Backlog" && data.backlog_detail) {
        html += renderBacklogDetail(data.backlog_detail);
    } else if (data.source === "Daily Report" && data.daily_report_detail) {
        html += renderDailyReportDetail(data.daily_report_detail);
    }

    if (data.parts && data.parts.length > 0) {
        html += renderPartsDetail(data.parts);
    }

    modalBody.innerHTML = html;
}

function renderBacklogDetail(backlog) {
    return `
        <hr class="mb-4">
        <h6 class="font-bold mb-2">Detail Backlog</h6>
        <div class="row">
            <div class="col-12">
                <table class="table table-sm">
                    <tr><td><strong>Problem Description</strong></td><td>${escapeHtml(
                        backlog.problem_description || ""
                    )}</td></tr>
                    <tr><td><strong>Backlog Job</strong></td><td>${escapeHtml(
                        backlog.backlog_job || ""
                    )}</td></tr>
                    <tr><td><strong>HM Found</strong></td><td>${
                        backlog.hm_found || "-"
                    }</td></tr>
                    <tr><td><strong>Plan HM</strong></td><td>${
                        backlog.plan_hm || "-"
                    }</td></tr>
                    <tr><td><strong>Plan Pull Date</strong></td><td>${formatRemainderDate(
                        backlog.plan_pull_date
                    )}</td></tr>
                    <tr><td><strong>Notes</strong></td><td>${escapeHtml(
                        backlog.notes || "-"
                    )}</td></tr>
                </table>
            </div>
        </div>
    `;
}

function renderDailyReportDetail(dailyReport) {
    let html = `
        <hr class="mb-4">
        <h6 class="font-bold mb-2">Detail Daily Report</h6>
        <div class="row">
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr><td><strong>Tanggal</strong></td><td>${formatRemainderDate(
                        dailyReport.date_in
                    )}</td></tr>
                    <tr><td><strong>HM</strong></td><td>${
                        dailyReport.hm || "-"
                    }</td></tr>
                    <tr><td><strong>Shift</strong></td><td>${escapeHtml(
                        dailyReport.shift || ""
                    )}</td></tr>
                    <tr><td><strong>Jam Masuk</strong></td><td>${escapeHtml(
                        dailyReport.hour_in || ""
                    )}</td></tr>
                    <tr><td><strong>Jam Keluar</strong></td><td>${escapeHtml(
                        dailyReport.hour_out || ""
                    )}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <table class="table table-sm">
                    <tr><td><strong>Problem</strong></td><td>${escapeHtml(
                        dailyReport.problem || "-"
                    )}</td></tr>
                    <tr><td><strong>Problem Component</strong></td><td>${escapeHtml(
                        dailyReport.problem_component || "-"
                    )}</td></tr>
                    <tr><td><strong>Plan Fix</strong></td><td>${escapeHtml(
                        dailyReport.plan_fix || "-"
                    )}</td></tr>
                    <tr><td><strong>Plan Rekomen</strong></td><td>${escapeHtml(
                        dailyReport.plan_rekomen || "-"
                    )}</td></tr>
                    <tr><td><strong>Lifetime Component</strong></td><td>${escapeHtml(
                        dailyReport.lifetime_component || "-"
                    )}</td></tr>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <h6 class="font-bold mt-2">Problem Description</h6>
                ${escapeHtml(
                        dailyReport.problem_description || "tidak ada deskripsi"
                    )}
            </div>
        </div>
    `;

    // Add jobs information if available
    if (dailyReport.jobs && dailyReport.jobs.length > 0) {
        html += `
        <div class="row mt-2">
            <div class="col-12">
                <h6 class="font-bold mt-2">Detail Pekerjaan</h6>
                <p>`;

        const jobDescriptions = dailyReport.jobs.map((job) =>
            escapeHtml(job.job_description || "tidak ada deskripsi")
        );
        html += jobDescriptions.join(", ");

        html += `
                </p>
            </div>
        </div>`;
    }

    // Add technicians information if available
    if (dailyReport.technicians && dailyReport.technicians.length > 0) {
        html += `
        <div class="row mt-2">
            <div class="col-12">
                <h6 class="font-bold mt-2">Teknisi</h6>
                <p>`;
        const technicianList = dailyReport.technicians.map((technician) => {
            const name = escapeHtml(technician.name || "tidak ada nama");
            return `${name}`;
        });
        html += technicianList.join(", ");
        html += `
                </p>
            </div>
        </div>`;
    }

    return html;
}

function renderPartsDetail(parts) {
    let html = `
        <hr class="mb-4">
        <h6 class="font-bold mb-2">Daftar Parts</h6>
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead class="table-dark">
                    <tr>
                        <th>Nama Part</th>
                        <th>Kode Part</th>
                        <th>Quantity</th>
                        <th>Stock</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
    `;

    parts.forEach((part) => {
        const stockClass = getStockStatusClass(
            part.stock_quantity,
            part.min_stock
        );
        const stockStatus = getStockStatusText(
            part.stock_quantity,
            part.min_stock
        );

        html += `
            <tr>
                <td>${escapeHtml(part.part_name || "")}</td>
                <td><code>${escapeHtml(part.part_code || "")}</code></td>
                <td>${part.quantity || "-"}</td>
                <td><span class="badge ${stockClass}">${
            part.stock_quantity || 0
        }</span></td>
                <td>${stockStatus}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    return html;
}

function getStockStatusText(stock, minStock) {
    const stockNum = parseInt(stock) || 0;
    const minStockNum = parseInt(minStock) || 0;

    if (stockNum === 0) {
        return '<span class="text-danger">Habis</span>';
    } else if (stockNum <= minStockNum) {
        return '<span class="text-warning">Hampir Habis</span>';
    } else {
        return '<span class="text-success">Tersedia</span>';
    }
}

function showRemainderLoading(show) {
    const loadingIndicator = document.getElementById(
        "remainderLoadingIndicator"
    );
    const table = document.getElementById("remainderTaskTable");
    const emptyState = document.getElementById("remainderEmptyState");

    if (loadingIndicator) {
        loadingIndicator.style.display = show ? "block" : "none";
    }

    if (table) {
        table.style.display = show ? "none" : "table";
    }

    if (emptyState && !show) {
        emptyState.style.display = "none";
    }
}

function showRemainderError(message) {
    const tableBody = document.getElementById("remainderTaskTableBody");
    if (tableBody) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="6" class="text-center text-danger py-4">
                    <i class="fas fa-exclamation-triangle mb-2"></i><br>
                    ${escapeHtml(message)}
                </td>
            </tr>
        `;
    }
}

function formatRemainderDate(dateString) {
    if (!dateString) return "-";

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString("id-ID", {
            day: "2-digit",
            month: "long",
            year: "numeric",
        });
    } catch (error) {
        return dateString;
    }
}

function escapeHtml(text) {
    if (typeof text !== "string") return text;

    const map = {
        "&": "&amp;",
        "<": "&lt;",
        ">": "&gt;",
        '"': "&quot;",
        "'": "&#039;",
    };

    return text.replace(/[&<>"']/g, function (m) {
        return map[m];
    });
}
