<?php
/**
 * Simple test script to check remainder tasks endpoint
 */

// Test data
$testData = [
    'start_date' => '2025-07-07',
    'end_date' => '2025-07-16'
];

// Simulate session data
session_start();
$_SESSION['site_id'] = 'PPA';
$_SESSION['role'] = 'adminsite';

echo "Testing Remainder Tasks Endpoint\n";
echo "================================\n";
echo "Test Data: " . json_encode($testData) . "\n";
echo "Session Site ID: " . ($_SESSION['site_id'] ?? 'Not set') . "\n";
echo "\n";

// Test URL
$url = 'http://127.0.0.1:8000/sites/remainder-tasks/data';

// Create context for POST request
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'X-CSRF-TOKEN: test-token'
        ],
        'content' => json_encode($testData)
    ]
]);

echo "Making request to: $url\n";
echo "Request method: POST\n";
echo "Request data: " . json_encode($testData) . "\n";
echo "\n";

// Make request
$response = file_get_contents($url, false, $context);

if ($response === false) {
    echo "❌ Request failed\n";
    echo "Error: " . error_get_last()['message'] . "\n";
} else {
    echo "✅ Request successful\n";
    echo "Response: " . $response . "\n";
    
    // Try to decode JSON
    $data = json_decode($response, true);
    if ($data) {
        echo "\n📊 Parsed Response:\n";
        echo "Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        if (isset($data['data'])) {
            echo "Data count: " . count($data['data']) . "\n";
            if (count($data['data']) > 0) {
                echo "First item: " . json_encode($data['data'][0], JSON_PRETTY_PRINT) . "\n";
            }
        }
        if (isset($data['message'])) {
            echo "Message: " . $data['message'] . "\n";
        }
    }
}

echo "\n";
echo "Test completed.\n";
